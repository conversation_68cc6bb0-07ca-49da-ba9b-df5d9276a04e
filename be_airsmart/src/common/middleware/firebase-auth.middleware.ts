/* eslint-disable @typescript-eslint/no-namespace */
import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { FirebaseService } from '../firebase/firebase.service';

// Define user data interface
interface UserData {
  role?: string;
  lockedCourses?: string[];
  [key: string]: any;
}

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        uid: string;
        email?: string;
        role?: string;
        lockedCourses?: string[];
      };
    }
  }
}

@Injectable()
export class FirebaseAuthMiddleware implements NestMiddleware {
  constructor(private readonly firebaseService: FirebaseService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Allow requests without token to pass through
        // Some endpoints might be public or handle auth differently
        return next();
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      if (!token) {
        return next();
      }

      // Verify Firebase token
      const firebaseAdmin = this.firebaseService.getAdmin();
      const decodedToken = await firebaseAdmin.auth().verifyIdToken(token);

      if (!decodedToken) {
        throw new UnauthorizedException('Invalid token');
      }

      // Get user data from Firestore
      const db = this.firebaseService.getDb();
      const userDoc = await db.collection('users').doc(decodedToken.uid).get();

      let userData: UserData | null = null;
      if (userDoc.exists) {
        userData = userDoc.data() as UserData;
      }

      // Attach user info to request
      req.user = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        role: userData?.role || 'installer',
        lockedCourses: userData?.lockedCourses || [],
      };

      next();
    } catch (error) {
      console.error('Firebase auth middleware error:', error);

      // Don't block requests on auth errors, let endpoints handle it
      // Some endpoints might not require authentication
      next();
    }
  }
}
