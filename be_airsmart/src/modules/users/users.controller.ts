/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from './dto/user.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Get all users with filtering, search, and pagination
   * @param query - Query parameters for filtering and pagination
   * @returns Paginated list of users with stats
   */
  @Get()
  async findAll(@Query() query: UserQueryDto) {
    return this.usersService.findAll(query);
  }

  /**
   * Get user by UID
   * @param uid - Firebase User UID
   * @returns User details
   */
  @Get(':uid')
  async findOne(@Param('uid') uid: string) {
    return this.usersService.findOne(uid);
  }

  /**
   * Create new user
   * @param createUserDto - User creation data
   * @returns Created user
   */
  @Post()
  async create(@Body() createUserDto: CreateUserDto) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    if (!validRoles.includes(createUserDto.role)) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    if (!createUserDto.email) {
      throw new BadRequestException('Email is required');
    }

    if (!createUserDto.password) {
      throw new BadRequestException('Password is required');
    }

    if (!createUserDto.displayName) {
      throw new BadRequestException('Display name is required');
    }

    return this.usersService.create(createUserDto);
  }

  /**
   * Update user
   * @param uid - Firebase User UID
   * @param updateUserDto - User update data
   * @returns Updated user
   */
  @Patch(':uid')
  async update(
    @Param('uid') uid: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    if (updateUserDto.role && !validRoles.includes(updateUserDto.role)) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    return this.usersService.update(uid, updateUserDto);
  }

  /**
   * Delete user
   * @param uid - Firebase User UID
   * @returns Success message
   */
  @Delete(':uid')
  async remove(@Param('uid') uid: string) {
    await this.usersService.remove(uid);
    return {
      success: true,
      message: `User with UID ${uid} has been deleted successfully`,
    };
  }

  /**
   * Get user statistics
   * @returns User statistics by role
   */
  @Get('stats/overview')
  async getStats() {
    const result = await this.usersService.findAll({});
    return {
      stats: result.stats,
      totalUsers: result.stats.total,
      roleDistribution: {
        installer: result.stats.installer,
        engineer: result.stats.engineer,
        architect: result.stats.architect,
        salesperson: result.stats.salesperson,
      },
    };
  }
  @Get(':userId/profile')
  async getUserProfile(@Param('userId') userId: string) {
    try {
      const profile = await this.usersService.getUserProfile(userId);
      if (!profile) {
        throw new NotFoundException('User not found');
      }

      return profile;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get user profile');
    }
  }

  /**
   * Get user role
   * @param userId - User ID
   * @returns User role
   */
  @Get(':userId/role')
  async getUserRole(@Param('userId') userId: string) {
    try {
      const role = await this.usersService.getUserRole(userId);
      return { role };
    } catch (error) {
      throw new BadRequestException('Failed to get user role');
    }
  }

  /**
   * Initialize user (called on first login)
   * @param userId - User ID
   * @param userData - User data
   * @returns User profile
   */
  @Post(':userId/initialize')
  async initializeUser(
    @Param('userId') userId: string,
    @Body()
    userData: {
      email: string;
      displayName?: string;
    },
  ) {
    try {
      if (!userData.email) {
        throw new BadRequestException('Email is required');
      }

      const profile = await this.usersService.initializeUser(
        userId,
        userData.email,
        userData.displayName,
      );

      return profile;
    } catch (error) {
      throw new BadRequestException('Failed to initialize user');
    }
  }

  /**
   * Get user locked courses
   * @param userId - User ID
   * @returns User locked courses
   */
  @Get(':userId/locked-courses')
  async getUserLockedCourses(@Param('userId') userId: string) {
    try {
      const user = await this.usersService.findOne(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      return {
        success: true,
        lockedCourses: user.lockedCourses || [],
        userId,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get user locked courses');
    }
  }

  /**
   * Lock course for user
   * @param userId - User ID
   * @param courseData - Course data containing courseId
   * @returns Success response
   */
  @Post(':userId/lock-course')
  async lockCourseForUser(
    @Param('userId') userId: string,
    @Body() courseData: { courseId: string },
  ) {
    try {
      const result = await this.usersService.lockCourseForUser(
        userId,
        courseData.courseId,
      );
      return {
        success: true,
        message: 'Course locked successfully',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException('Failed to lock course for user');
    }
  }

  /**
   * Unlock course for user
   * @param userId - User ID
   * @param courseId - Course ID to unlock
   * @returns Success response
   */
  @Delete(':userId/unlock-course/:courseId')
  async unlockCourseForUser(
    @Param('userId') userId: string,
    @Param('courseId') courseId: string,
  ) {
    try {
      const result = await this.usersService.unlockCourseForUser(
        userId,
        courseId,
      );
      return {
        success: true,
        message: 'Course unlocked successfully',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException('Failed to unlock course for user');
    }
  }

  /**
   * Update locked courses for user
   * @param userId - User ID
   * @param body - { lockedCourses: string[] }
   * @returns Success response
   */
  @Patch(':userId/locked-courses')
  async updateUserLockedCourses(
    @Param('userId') userId: string,
    @Body() body: { lockedCourses: string[] },
  ) {
    try {
      const result = await this.usersService.updateUserLockedCourses(userId, body.lockedCourses);
      return {
        success: true,
        message: 'Locked courses updated successfully',
        data: {
          userId,
          lockedCourses: result.lockedCourses || [],
        },
      };
    } catch (error) {
      throw new BadRequestException('Failed to update locked courses');
    }
  }
}
