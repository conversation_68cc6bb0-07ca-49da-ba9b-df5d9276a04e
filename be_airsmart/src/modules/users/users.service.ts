/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { User } from './entities/user.entity';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from './dto/user.dto';
import { FirebaseService } from '../../common/firebase/firebase.service';
import * as admin from 'firebase-admin';
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}
@Injectable()
export class UsersService {
  constructor(private readonly firebaseService: FirebaseService) {}
  /**
   * Get user profile with role
   * @param userId - User ID
   * @returns User profile with role
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const db = this.firebaseService.getDb();
      const userDoc = await db.collection('users').doc(userId).get();

      if (!userDoc.exists) {
        return null;
      }

      const userData = userDoc.data();
      if (!userData) {
        return null;
      }

      return {
        uid: userId,
        email: userData.email || '',
        displayName: userData.displayName || '',
        role: userData.role || 'installer', // Default role
        createdAt: userData.createdAt?.toDate() || new Date(),
        updatedAt: userData.updatedAt?.toDate() || new Date(),
      };
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }
  async getUserRole(userId: string): Promise<string> {
    try {
      const userProfile = await this.getUserProfile(userId);
      return userProfile?.role || 'installer';
    } catch (error) {
      console.error('Error getting user role:', error);
      return 'installer'; // Default fallback
    }
  }
  async createOrUpdateUser(
    userId: string,
    userData: {
      email: string;
      displayName?: string;
      role?: string;
    },
  ): Promise<UserProfile> {
    try {
      const db = this.firebaseService.getDb();
      const userRef = db.collection('users').doc(userId);

      const existingUser = await userRef.get();
      const now = new Date();

      const userProfile: Partial<UserProfile> = {
        uid: userId,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role || 'installer',
        updatedAt: now,
      };

      if (!existingUser.exists) {
        userProfile.createdAt = now;
      }

      await userRef.set(userProfile, { merge: true });

      return {
        uid: userId,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role || 'installer',
        createdAt: existingUser.exists
          ? existingUser.data()?.createdAt?.toDate() || now
          : now,
        updatedAt: now,
      };
    } catch (error) {
      console.error('Error creating/updating user:', error);
      throw error;
    }
  }
  async userExists(userId: string): Promise<boolean> {
    try {
      const db = this.firebaseService.getDb();
      const userDoc = await db.collection('users').doc(userId).get();
      return userDoc.exists;
    } catch (error) {
      console.error('Error checking if user exists:', error);
      return false;
    }
  }
  async initializeUser(
    userId: string,
    email: string,
    displayName?: string,
  ): Promise<UserProfile> {
    try {
      const exists = await this.userExists(userId);

      if (!exists) {
        // Create new user with default role
        return this.createOrUpdateUser(userId, {
          email,
          displayName,
          role: 'installer', // Default role for new users
        });
      } else {
        // Return existing user
        const existingProfile = await this.getUserProfile(userId);
        if (!existingProfile) {
          throw new Error('Failed to get existing user profile');
        }
        return existingProfile;
      }
    } catch (error) {
      console.error('Error initializing user:', error);
      throw error;
    }
  }
  async findAll(query: UserQueryDto) {
    try {
      const firestore = this.firebaseService.getDb();
      let usersQuery: admin.firestore.Query<admin.firestore.DocumentData> =
        firestore.collection('users');

      // Apply role filter first (single field index)
      if (query.role && query.role !== 'all') {
        usersQuery = usersQuery.where('role', '==', query.role);
      }

      // Get all results without server-side sorting to avoid composite index requirement
      const snapshot = await usersQuery.get();
      let users: User[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        users.push({
          uid: doc.id,
          email: data.email,
          displayName: data.displayName,
          role: data.role,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        });
      });

      // Apply search filter
      if (query.search) {
        const searchLower = query.search.toLowerCase();
        users = users.filter(
          (user) =>
            user.displayName?.toLowerCase().includes(searchLower) ||
            user.email?.toLowerCase().includes(searchLower),
        );
      }

      // Apply client-side sorting
      const sortBy = query.sortBy || 'createdAt';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
      users.sort((a, b) => {
        const aVal = a[sortBy as keyof User];
        const bVal = b[sortBy as keyof User];

        // Handle undefined values
        if (aVal === undefined && bVal === undefined) return 0;
        if (aVal === undefined) return 1; // Put undefined values at the end
        if (bVal === undefined) return -1; // Put undefined values at the end

        if (aVal < bVal) return -1 * sortOrder;
        if (aVal > bVal) return 1 * sortOrder;
        return 0;
      });

      // Apply pagination
      const page = query.page || 1;
      const limit = query.limit || 10;
      const offset = (page - 1) * limit;
      const total = users.length;
      const totalPages = Math.ceil(total / limit);

      const paginatedUsers = users.slice(offset, offset + limit);

      // Calculate statistics
      const stats = {
        total: users.length,
        installer: users.filter((u) => u.role === 'installer').length,
        engineer: users.filter((u) => u.role === 'engineer').length,
        architect: users.filter((u) => u.role === 'architect').length,
        salesperson: users.filter((u) => u.role === 'salesperson').length,
      };

      return {
        users: paginatedUsers,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        stats,
        filters: {
          search: query.search || '',
          role: query.role || 'all',
        },
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      throw new BadRequestException('Failed to fetch users');
    }
  }

  async findOne(uid: string): Promise<User> {
    try {
      const firestore = this.firebaseService.getDb();
      const userDoc = await firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        throw new NotFoundException(`User with UID ${uid} not found`);
      }

      const data = userDoc.data();
      if (!data) {
        throw new NotFoundException(`User data not found for UID ${uid}`);
      }

      return {
        uid: userDoc.id,
        email: data.email,
        displayName: data.displayName,
        role: data.role,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        lockedCourses: data.lockedCourses || [], // Add this field
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to fetch user: ${error.message}`);
    }
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      const firebaseAdmin = this.firebaseService.getAdmin();
      const firestore = this.firebaseService.getDb();

      // Create user in Firebase Authentication
      const userRecord = await firebaseAdmin.auth().createUser({
        email: createUserDto.email,
        password: createUserDto.password,
        displayName: createUserDto.displayName,
      });

      // Set custom claims for role
      await firebaseAdmin.auth().setCustomUserClaims(userRecord.uid, {
        role: createUserDto.role,
      });

      // Save user data to Firestore
      const userData = {
        email: createUserDto.email,
        displayName: createUserDto.displayName,
        role: createUserDto.role,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await firestore.collection('users').doc(userRecord.uid).set(userData);

      return {
        uid: userRecord.uid,
        ...userData,
      };
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        throw new BadRequestException(
          `User with email ${createUserDto.email} already exists`,
        );
      }
      throw new BadRequestException(`Failed to create user: ${error.message}`);
    }
  }

  async update(uid: string, updateUserDto: UpdateUserDto): Promise<User> {
    try {
      const firebaseAdmin = this.firebaseService.getAdmin();
      const firestore = this.firebaseService.getDb();

      // Update Firebase Authentication user
      const updateData: any = {};
      if (updateUserDto.email) updateData.email = updateUserDto.email;
      if (updateUserDto.displayName)
        updateData.displayName = updateUserDto.displayName;

      if (Object.keys(updateData).length > 0) {
        await firebaseAdmin.auth().updateUser(uid, updateData);
      }

      // Update custom claims if role changed
      if (updateUserDto.role) {
        await firebaseAdmin.auth().setCustomUserClaims(uid, {
          role: updateUserDto.role,
        });
      }

      // Update Firestore user data
      const firestoreData: any = {};
      if (updateUserDto.displayName)
        firestoreData.displayName = updateUserDto.displayName;
      if (updateUserDto.email) firestoreData.email = updateUserDto.email;
      if (updateUserDto.role) firestoreData.role = updateUserDto.role;

      if (Object.keys(firestoreData).length > 0) {
        firestoreData.updatedAt = new Date();
        await firestore.collection('users').doc(uid).update(firestoreData);
      }

      return this.findOne(uid);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        throw new NotFoundException(`User with UID ${uid} not found`);
      }
      if (error.code === 'auth/email-already-exists') {
        throw new BadRequestException(
          `Email ${updateUserDto.email} is already in use`,
        );
      }
      throw new BadRequestException(`Failed to update user: ${error.message}`);
    }
  }

  async remove(uid: string): Promise<void> {
    try {
      const firebaseAdmin = this.firebaseService.getAdmin();
      const firestore = this.firebaseService.getDb();

      // Delete from Firebase Authentication
      await firebaseAdmin.auth().deleteUser(uid);

      // Delete from Firestore
      await firestore.collection('users').doc(uid).delete();
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        throw new NotFoundException(`User with UID ${uid} not found`);
      }
      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }

  /**
   * Lock course for user
   * @param userId - User ID
   * @param courseId - Course ID to lock
   * @returns Updated user data
   */
  async lockCourseForUser(userId: string, courseId: string) {
    try {
      const firestore = this.firebaseService.getDb();
      const userRef = firestore.collection('users').doc(userId);

      // Get current user data
      const userDoc = await userRef.get();
      if (!userDoc.exists) {
        throw new NotFoundException(`User with UID ${userId} not found`);
      }

      const userData = userDoc.data();
      if (!userData) {
        throw new NotFoundException(`User data not found for UID ${userId}`);
      }

      const currentLockedCourses = userData.lockedCourses || [];

      // Add course to locked list if not already locked
      if (!currentLockedCourses.includes(courseId)) {
        const updatedLockedCourses = [
          ...currentLockedCourses,
          courseId,
        ];

        await userRef.update({
          lockedCourses: updatedLockedCourses,
          updatedAt: new Date(),
        });

        return {
          userId,
          courseId,
          lockedCourses: updatedLockedCourses,
        };
      }

      return {
        userId,
        courseId,
        lockedCourses: currentLockedCourses,
        message: 'Course already locked',
      };
    } catch (error) {
      console.error('Error locking course for user:', error);
      throw new BadRequestException(
        `Failed to lock course: ${error.message}`,
      );
    }
  }

  /**
   * Unlock course for user
   * @param userId - User ID
   * @param courseId - Course ID to unlock
   * @returns Updated user data
   */
  async unlockCourseForUser(userId: string, courseId: string) {
    try {
      const firestore = this.firebaseService.getDb();
      const userRef = firestore.collection('users').doc(userId);

      // Get current user data
      const userDoc = await userRef.get();
      if (!userDoc.exists) {
        throw new NotFoundException(`User with UID ${userId} not found`);
      }

      const userData = userDoc.data();
      if (!userData) {
        throw new NotFoundException(`User data not found for UID ${userId}`);
      }

      const currentLockedCourses = userData.lockedCourses || [];

      // Remove course from locked list
      const updatedLockedCourses = currentLockedCourses.filter(
        (id: string) => id !== courseId,
      );

      await userRef.update({
        lockedCourses: updatedLockedCourses,
        updatedAt: new Date(),
      });

      return {
        userId,
        courseId,
        lockedCourses: updatedLockedCourses,
      };
    } catch (error) {
      console.error('Error unlocking course for user:', error);
      throw new BadRequestException(
        `Failed to unlock course: ${error.message}`,
      );
    }
  }

  /**
   * Update locked courses for user
   * @param userId - User ID
   * @param lockedCourses - Array of course IDs
   * @returns Updated user data
   */
  async updateUserLockedCourses(userId: string, lockedCourses: string[]) {
    try {
      const firestore = this.firebaseService.getDb();
      const userRef = firestore.collection('users').doc(userId);

      // Get current user data
      const userDoc = await userRef.get();
      if (!userDoc.exists) {
        throw new NotFoundException(`User with UID ${userId} not found`);
      }

      // Update lockedCourses
      await userRef.update({
        lockedCourses: lockedCourses || [],
        updatedAt: new Date(),
      });

      // Return updated user
      const updatedUserDoc = await userRef.get();
      const updatedUserData = updatedUserDoc.data();
      return {
        uid: userId,
        lockedCourses: lockedCourses || [],
        ...updatedUserData,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update locked courses: ${error.message}`,
      );
    }
  }
}
