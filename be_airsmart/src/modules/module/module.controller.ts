import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
} from '@nestjs/common';
import { ModuleService } from './module.service';
import { CreateModuleDto } from './dto/create-module.dto';
import { UpdateModuleDto } from './dto/update-module.dto';
import { Request } from 'express';

@Controller('module')
export class ModuleController {
  constructor(private readonly moduleService: ModuleService) {}

  @Post()
  create(@Body() createModuleDto: CreateModuleDto) {
    return this.moduleService.create(createModuleDto);
  }

  @Get()
  async findAll() {
    return this.moduleService.getAllModules();
  }

  @Get('courses')
  async getCoursesWithModules(@Req() req: Request) {
    // Extract user info from request (added by Firebase auth middleware)
    const userInfo = req.user
      ? {
          uid: req.user.uid,
          lockedCourses: req.user.lockedCourses || [],
        }
      : undefined;

    return this.moduleService.getCoursesWithModules(userInfo);
  }

  @Get('container/:containerId/modules')
  async getModulesFromContainer(@Param('containerId') containerId: string) {
    return this.moduleService.getModulesFromContainer(containerId);
  }

  @Get(':moduleId')
  findOne(@Param('moduleId') moduleId: string) {
    return this.moduleService.findOne(moduleId);
  }

  @Get(':moduleId/steps')
  async getSteps(@Param('moduleId') moduleId: string) {
    return this.moduleService.getStepsByModuleId(moduleId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateModuleDto: UpdateModuleDto) {
    return this.moduleService.update(+id, updateModuleDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.moduleService.remove(+id);
  }
}
