/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { CreateModuleDto } from './dto/create-module.dto';
import { UpdateModuleDto } from './dto/update-module.dto';
import { CreateCourseDto } from './dto/create-course.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { CreateStepDto } from './dto/create-step.dto';
import { UpdateStepDto } from './dto/update-step.dto';
import { CreateQuizQuestionDto } from './dto/create-quiz.dto';
import { UpdateQuizQuestionDto } from './dto/update-quiz.dto';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import { WasabiService } from 'src/common/wasabi/wasabi.service';

// Helper function to remove undefined values
function removeUndefinedValues(obj: any): any {
  const result = {};
  for (const key in obj) {
    if (obj[key] !== undefined) {
      result[key] = obj[key];
    }
  }
  return result;
}

@Injectable()
export class ModuleService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly wasabiService: WasabiService,
  ) {}

  create(createModuleDto: CreateModuleDto) {
    return 'This action adds a new module';
  }

  async getAllModules() {
    const snapshot = await this.firebaseService
      .getModulesCollection()
      .orderBy('order', 'asc')
      .get();
    const modules = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    return modules;
  }

  /**
   * Get all courses with their modules
   * @param userInfo - User information from request (optional)
   * @returns Courses with modules organized by course
   */
  async getCoursesWithModules(userInfo?: {
    uid: string;
    lockedCourses?: string[];
  }) {
    try {
      const db = this.firebaseService.getDb();

      // Get all courses
      const coursesSnapshot = await db
        .collection('courses')
        .orderBy('order', 'asc')
        .get();

      const result: any[] = [];

      for (const courseDoc of coursesSnapshot.docs) {
        const courseData = courseDoc.data();

        // Skip inactive courses
        if (courseData.isActive === false) {
          continue;
        }

        // Skip locked courses for this user
        if (userInfo?.lockedCourses?.includes(courseDoc.id)) {
          continue;
        }

        // Get modules for this course (without orderBy to avoid composite index)
        const modulesSnapshot = await db
          .collection('modules')
          .where('course', '==', courseDoc.id)
          .get();

        const modules = modulesSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as any[];

        // Sort modules by courseOrder in memory
        modules.sort((a: any, b: any) => {
          const orderA = a.courseOrder || 0;
          const orderB = b.courseOrder || 0;
          return orderA - orderB;
        });

        // Create course with modules
        const courseWithModules = {
          id: courseDoc.id,
          ...courseData,
          modules,
          totalModules: modules.length,
          totalSteps: modules.reduce(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            (sum: number, module: any) => sum + (module.totalSteps || 0),
            0,
          ),
          totalQuizQuestions: modules.reduce(
            (sum: number, module: any) =>
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              sum + (module.totalQuizQuestions || 0),
            0,
          ),
          type: 'container',
          isContainer: true,
        };

        result.push(courseWithModules);
      }

      // Get standalone modules (modules without course)
      const standaloneModulesSnapshot = await db
        .collection('modules')
        .where('course', '==', null)
        .get();

      const standaloneModules = standaloneModulesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        type: 'module',
      })) as any[];

      // Sort standalone modules by order in memory
      standaloneModules.sort((a: any, b: any) => {
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderA - orderB;
      });

      // Add standalone modules to result
      result.push(...standaloneModules);
      return result;
    } catch (error) {
      console.error('Error getting courses with modules:', error);
      throw error;
    }
  }

  async getModulesFromContainer(containerId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get modules by course
      const snapshot = await db
        .collection('modules')
        .where('course', '==', containerId)
        .orderBy('courseOrder', 'asc')
        .get();

      const modules = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return modules;
    } catch (error) {
      console.error('Error getting modules from container:', error);
      return [];
    }
  }

  /**
   * Get course information by ID
   * @param courseId - The course ID
   * @returns Course data
   */
  async getCourseById(courseId: string) {
    try {
      const db = this.firebaseService.getDb();
      const courseDoc = await db
        .collection('courses')
        .doc(courseId)
        .get();

      if (!courseDoc.exists) {
        return null;
      }

      return {
        id: courseDoc.id,
        ...courseDoc.data(),
      };
    } catch (error) {
      console.error('Error getting course:', error);
      return null;
    }
  }

  async findOne(moduleId: string) {
    const moduleData = await this.firebaseService.getModuleById(moduleId);
    if (!moduleData) {
      return null;
    }
    const steps = await this.firebaseService.getStepsByModuleId(moduleId);
    return {
      ...moduleData,
      steps,
    };
  }

  async getStepsByModuleId(moduleId: string) {
    return this.firebaseService.getStepsByModuleId(moduleId);
  }

  update(id: number, updateModuleDto: UpdateModuleDto) {
    return `This action updates a #${id} module`;
  }

  remove(id: number) {
    return `This action removes a #${id} module`;
  }

  // ========================================
  // ADMIN-ONLY METHODS (SAFE TO ADD)
  // ========================================

  /**
   * [ADMIN] Create a new module
   * @param createModuleDto - Module data
   * @returns Created module with ID
   */
  async createModuleAdmin(createModuleDto: CreateModuleDto) {
    try {
      const db = this.firebaseService.getDb();

      // Generate order if not provided - SIMPLIFIED to avoid index requirements
      let finalOrder = createModuleDto.order || 1;

      if (!createModuleDto.order) {
        // Get all modules and calculate order manually to avoid complex queries
        const allModulesSnapshot = await db.collection('modules').get();

        if (createModuleDto.course) {
          // Find max courseOrder for this course
          let maxCourseOrder = 0;
          allModulesSnapshot.docs.forEach((doc) => {
            const data = doc.data();
            if (
              data.course === createModuleDto.course &&
              data.courseOrder
            ) {
              maxCourseOrder = Math.max(maxCourseOrder, data.courseOrder);
            }
          });
          finalOrder = maxCourseOrder + 1;
        } else {
          // Find max order for standalone modules
          let maxOrder = 0;
          allModulesSnapshot.docs.forEach((doc) => {
            const data = doc.data();
            if (!data.course && data.order) {
              maxOrder = Math.max(maxOrder, data.order);
            }
          });
          finalOrder = maxOrder + 1;
        }
      }

      // Prepare module data
      const moduleData = {
        ...createModuleDto,
        ...(createModuleDto.course
          ? { courseOrder: finalOrder }
          : { order: finalOrder }),
        createdAt: new Date(),
        updatedAt: new Date(),
        // Default values
        isLocked: createModuleDto.isLocked ?? false,
        passingScore: createModuleDto.passingScore ?? 70,
        totalSteps: createModuleDto.totalSteps ?? 0,
        totalQuizQuestions: createModuleDto.totalQuizQuestions ?? 0,
        tags: createModuleDto.tags ?? [],
      };

      // Create module document
      const docRef = await db.collection('modules').add(moduleData);

      console.log(`✅ Admin created module: ${docRef.id}`);

      return {
        id: docRef.id,
        ...moduleData,
      };
    } catch (error) {
      console.error('Error creating module (admin):', error);
      throw new Error(`Failed to create module: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Update an existing module
   * @param id - Module ID
   * @param updateModuleDto - Update data
   * @returns Updated module
   */
  async updateModuleAdmin(id: string, updateModuleDto: UpdateModuleDto) {
    try {
      const db = this.firebaseService.getDb();

      // Check if module exists
      const moduleRef = db.collection('modules').doc(id);
      const moduleDoc = await moduleRef.get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      // Prepare update data
      const updateData = {
        ...updateModuleDto,
        updatedAt: new Date(),
      };

      // Handle course change - need to reorder
      const currentData = moduleDoc.data();
      if (
        currentData &&
        updateModuleDto.course !== undefined &&
        updateModuleDto.course !== currentData.course
      ) {
        // Moving between courses or to/from standalone
        if (updateModuleDto.course) {
          // Moving to a course - get new courseOrder
          const lastInNewCourse = await db
            .collection('modules')
            .where('course', '==', updateModuleDto.course)
            .orderBy('courseOrder', 'desc')
            .limit(1)
            .get();

          updateData.courseOrder = lastInNewCourse.empty
            ? 1
            : lastInNewCourse.docs[0].data().courseOrder + 1;
          // Remove global order
          delete updateData.order;
        } else {
          // Moving to standalone - get new global order
          const lastStandalone = await db
            .collection('modules')
            .where('course', '==', null)
            .orderBy('order', 'desc')
            .limit(1)
            .get();

          updateData.order = lastStandalone.empty
            ? 1
            : lastStandalone.docs[0].data().order + 1;
          // Remove course order
          delete updateData.courseOrder;
        }
      }

      // Update module
      await moduleRef.update(updateData);

      // Get updated document
      const updatedDoc = await moduleRef.get();

      console.log(`✅ Admin updated module: ${id}`);

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    } catch (error: any) {
      console.error('Error updating module (admin):', error);
      throw new Error(
        `Failed to update module: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Delete a module and all related data
   * @param id - Module ID
   * @returns Success result with cleanup info
   */
  async deleteModuleAdmin(id: string) {
    try {
      const db = this.firebaseService.getDb();

      // Check if module exists
      const moduleRef = db.collection('modules').doc(id);
      const moduleDoc = await moduleRef.get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      // SKIP progress check for now to avoid index requirements
      // TODO: Add proper progress check when Firebase indexes are set up
      console.log(
        '⚠️ Skipping user progress check to avoid index requirements',
      );

      // Get module data for cleanup
      const moduleData = moduleDoc.data();

      // Get all steps and quiz questions for cleanup and counting
      const stepsQuery = await db
        .collection('modules')
        .doc(id)
        .collection('steps')
        .get();

      const quizQuery = await db
        .collection('modules')
        .doc(id)
        .collection('quiz')
        .get();

      // Collect all files to cleanup from module and its steps
      const filesToCleanup: string[] = [];

      // Add module's 3D model file if exists
      if (moduleData?.modelUrl) {
        console.log(
          '🔍 Found module 3D model for cleanup:',
          moduleData.modelUrl,
        );
        filesToCleanup.push(moduleData.modelUrl);
      }

      // Add files from all steps
      stepsQuery.docs.forEach((stepDoc) => {
        const stepData = stepDoc.data();
        if (stepData?.imageUrl) {
          console.log('🔍 Found step image for cleanup:', stepData.imageUrl);
          filesToCleanup.push(stepData.imageUrl);
        }
        if (stepData?.videoUrl) {
          console.log('🔍 Found step video for cleanup:', stepData.videoUrl);
          filesToCleanup.push(stepData.videoUrl);
        }
      });

      console.log(`🔍 Total files to cleanup: ${filesToCleanup.length}`);

      // Batch delete subcollections
      const batch = db.batch();

      stepsQuery.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      quizQuery.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      // Delete main module document
      batch.delete(moduleRef);

      await batch.commit();

      console.log(`✅ Admin deleted module: ${id} and related data`);

      return {
        success: true,
        id,
        deletedSteps: stepsQuery.docs.length,
        deletedQuizQuestions: quizQuery.docs.length,
        moduleData, // Return module data for file cleanup
        stepsData: stepsQuery.docs.map((doc) => doc.data()), // Return steps data for file cleanup
        filesToCleanup: filesToCleanup.length,
      };
    } catch (error) {
      console.error('Error deleting module (admin):', error);
      throw new Error(`Failed to delete module: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Get single module with admin metadata
   * @param id - Module ID
   * @returns Module with steps and quiz questions
   */
  async getModuleAdmin(id: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get module document
      const moduleDoc = await db.collection('modules').doc(id).get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      // Get steps and quiz questions
      const [stepsSnapshot, quizSnapshot] = await Promise.all([
        db
          .collection('modules')
          .doc(id)
          .collection('steps')
          .orderBy('order', 'asc')
          .get(),
        db
          .collection('modules')
          .doc(id)
          .collection('quiz')
          .orderBy('order', 'asc')
          .get(),
      ]);

      const steps = stepsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || null,
        updatedAt: doc.data().updatedAt?.toDate?.() || null,
      }));

      const quizQuestions = quizSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || null,
        updatedAt: doc.data().updatedAt?.toDate?.() || null,
      }));

      return {
        id: moduleDoc.id,
        ...moduleDoc.data(),
        actualStepsCount: steps.length,
        actualQuizCount: quizQuestions.length,
        steps,
        quizQuestions,
        createdAt: moduleDoc.data()?.createdAt?.toDate?.() || null,
        lastUpdated: moduleDoc.data()?.lastUpdated?.toDate?.() || null,
      };
    } catch (error: any) {
      console.error('Error getting admin module:', error);
      throw new Error(
        `Failed to get module: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Get all modules with admin-specific data
   * @returns All modules with metadata
   */
  async getModulesAdmin() {
    try {
      const db = this.firebaseService.getDb();

      // Get all modules WITHOUT orderBy to avoid FAILED_PRECONDITION
      const modulesSnapshot = await db.collection('modules').get();

      const modules = await Promise.all(
        modulesSnapshot.docs.map(async (doc) => {
          const moduleData = doc.data();

          // Get additional admin metadata
          const stepsCount = await db
            .collection('modules')
            .doc(doc.id)
            .collection('steps')
            .get();

          const quizCount = await db
            .collection('modules')
            .doc(doc.id)
            .collection('quiz')
            .get();

          // TEMPORARILY COMMENT OUT - REQUIRES FIRESTORE INDEX
          // Get user progress count
          // const progressCount = await db
          //   .collectionGroup('moduleProgress')
          //   .where('moduleId', '==', doc.id)
          //   .get();

          return {
            id: doc.id,
            ...moduleData,
            // Admin metadata
            actualStepsCount: stepsCount.size,
            actualQuizCount: quizCount.size,
            userProgressCount: 0, // progressCount.size, // TEMPORARILY SET TO 0
            lastUpdated: moduleData.updatedAt?.toDate?.() || null,
            createdAt: moduleData.createdAt?.toDate?.() || null,
          };
        }),
      );

      // Sort modules in memory by order (standalone) or courseOrder (in course)
      modules.sort((a: any, b: any) => {
        // If both have global order, sort by order
        if (a.order && b.order) {
          return a.order - b.order;
        }

        // If both have courseOrder, sort by courseOrder
        if (a.courseOrder && b.courseOrder) {
          return a.courseOrder - b.courseOrder;
        }

        // Mixed case: put modules with global order first
        if (a.order && !b.order) return -1;
        if (!a.order && b.order) return 1;

        // Fallback: sort by creation date or name
        if (a.createdAt && b.createdAt) {
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        }

        return (a.name || '').localeCompare(b.name || '');
      });

      return modules;
    } catch (error: any) {
      console.error('Error getting modules (admin):', error);
      throw new Error(
        `Failed to get modules: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Create a new course
   * @param createCourseDto - Course data
   * @returns Created course
   */
  async createCourseAdmin(createCourseDto: CreateCourseDto) {
    try {
      const db = this.firebaseService.getDb();

      // Generate order if not provided
      let finalOrder = createCourseDto.order;
      if (!finalOrder) {
        const lastCourse = await db
          .collection('courses')
          .orderBy('order', 'desc')
          .limit(1)
          .get();

        finalOrder = lastCourse.empty ? 1 : lastCourse.docs[0].data().order + 1;
      }

      // Prepare course data
      const courseData = {
        ...createCourseDto,
        order: finalOrder,
        createdAt: new Date(),
        updatedAt: new Date(),
        // Default values
        isActive: createCourseDto.isActive ?? true,
        isLocked: createCourseDto.isLocked ?? false,
        isComingSoon: createCourseDto.isComingSoon ?? false,
        tags: createCourseDto.tags ?? [],
      };

      // Create course document
      const docRef = await db.collection('courses').add(courseData);

      console.log(`✅ Admin created course: ${docRef.id}`);

      return {
        id: docRef.id,
        ...courseData,
      };
    } catch (error) {
      console.error('Error creating course (admin):', error);
      throw new Error(`Failed to create course: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Update an existing course
   * @param id - Course ID
   * @param updateCourseDto - Update data
   * @returns Updated course
   */
  async updateCourseAdmin(id: string, updateCourseDto: UpdateCourseDto) {
    try {
      const db = this.firebaseService.getDb();

      // Check if course exists
      const courseRef = db.collection('courses').doc(id);
      const courseDoc = await courseRef.get();

      if (!courseDoc.exists) {
        throw new Error('Course not found');
      }

      // Get existing course data to check for old thumbnail
      const existingCourseData = courseDoc.data();

      // Clean up old thumbnail from Wasabi if thumbnail is being changed
      let thumbnailCleanedUp = false;
      if (
        existingCourseData?.thumbnail &&
        updateCourseDto.thumbnail &&
        existingCourseData.thumbnail !== updateCourseDto.thumbnail
      ) {
        console.log(
          '🗑️ Cleaning up old course thumbnail:',
          existingCourseData.thumbnail,
        );
        try {
          thumbnailCleanedUp = await this.wasabiService.deleteFileByUrl(
            existingCourseData.thumbnail,
          );
          if (thumbnailCleanedUp) {
            console.log('✅ Old course thumbnail deleted from Wasabi');
          } else {
            console.log('⚠️ Failed to delete old course thumbnail from Wasabi');
          }
        } catch (cleanupError) {
          console.error(
            '❌ Error cleaning up old course thumbnail:',
            cleanupError,
          );
          // Don't fail the main operation if cleanup fails
        }
      }

      // Prepare update data
      const updateData = {
        ...updateCourseDto,
        updatedAt: new Date(),
      };

      // Update course
      await courseRef.update(updateData);

      // Get updated document
      const updatedDoc = await courseRef.get();

      console.log(`✅ Admin updated course: ${id}`);

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
        thumbnailCleanedUp,
      };
    } catch (error) {
      console.error('Error updating course (admin):', error);
      throw new Error(`Failed to update course: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Delete a course
   * @param id - Course ID
   * @returns Success result
   */
  async deleteCourseAdmin(id: string) {
    try {
      const db = this.firebaseService.getDb();

      // Check if course exists
      const courseRef = db.collection('courses').doc(id);
      const courseDoc = await courseRef.get();

      if (!courseDoc.exists) {
        throw new Error('Course not found');
      }

      // Get course data for cleanup
      const courseData = courseDoc.data();

      // Check for modules in this course
      const modulesInCourse = await db
        .collection('modules')
        .where('course', '==', id)
        .limit(1)
        .get();

      if (!modulesInCourse.empty) {
        throw new Error(
          'Cannot delete course that contains modules. Move or delete modules first.',
        );
      }
      // Clean up thumbnail file from Wasabi if it exists
      let thumbnailCleanedUp = false;
      if (courseData?.thumbnail) {
        console.log('🗑️ Cleaning up course thumbnail:', courseData.thumbnail);
        try {
          thumbnailCleanedUp = await this.wasabiService.deleteFileByUrl(
            courseData.thumbnail,
          );
          if (thumbnailCleanedUp) {
            console.log('✅ Course thumbnail deleted from Wasabi');
          } else {
            console.log('⚠️ Failed to delete course thumbnail from Wasabi');
          }
        } catch (cleanupError) {
          console.error('❌ Error cleaning up course thumbnail:', cleanupError);
          // Don't fail the main operation if cleanup fails
        }
      }

      // Safe to delete course from database
      await courseRef.delete();

      console.log(`✅ Admin deleted course: ${id}`);

      return {
        success: true,
        id,
        thumbnailCleanedUp,
      };
    } catch (error) {
      console.error('Error deleting course (admin):', error);
      throw new Error(`Failed to delete course: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Get all courses with admin metadata
   * @returns All courses with module counts
   */
  async getCoursesAdmin() {
    try {
      const db = this.firebaseService.getDb();

      // Get all courses
      const coursesSnapshot = await db
        .collection('courses')
        .orderBy('order', 'asc')
        .get();

      const courses = await Promise.all(
        coursesSnapshot.docs.map(async (doc) => {
          const courseData = doc.data();

          // Get modules count in this course
          const modulesCount = await db
            .collection('modules')
            .where('course', '==', doc.id)
            .get();

          return {
            id: doc.id,
            ...courseData,
            // Admin metadata
            moduleCount: modulesCount.size,
            lastUpdated: courseData.updatedAt?.toDate?.() || null,
            createdAt: courseData.createdAt?.toDate?.() || null,
          };
        }),
      );

      return courses;
    } catch (error) {
      console.error('Error getting courses (admin):', error);
      throw new Error(`Failed to get courses: ${error.message}`);
    }
  }

  /**
   * [ADMIN] Create a new step for a module
   * @param moduleId - Module ID
   * @param createStepDto - Step data
   * @returns Created step
   */
  async createStepAdmin(moduleId: string, createStepDto: CreateStepDto) {
    try {
      const db = this.firebaseService.getDb();

      // Check if module exists
      const moduleRef = db.collection('modules').doc(moduleId);
      const moduleDoc = await moduleRef.get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      // Generate order if not provided
      let finalOrder = createStepDto.order;
      if (!finalOrder) {
        const lastStep = await db
          .collection('modules')
          .doc(moduleId)
          .collection('steps')
          .orderBy('order', 'desc')
          .limit(1)
          .get();

        finalOrder = lastStep.empty ? 1 : lastStep.docs[0].data().order + 1;
      }

      // Prepare step data with default values to match database structure
      const stepData = removeUndefinedValues({
        order: finalOrder,
        title: createStepDto.title,
        content: createStepDto.content,
        camera: createStepDto.camera || {
          position: [0, 1, 5],
          target: [0, 0, 0],
        },
        hotspots: createStepDto.hotspots || [
          {
            id: `hotspot-${finalOrder}`,
            position: [0, 0, 0],
            label: createStepDto.title,
            action: 'highlight',
            description: createStepDto.content.substring(0, 100) + '...',
          },
        ],
        // Remove default actions and introjsSteps - only use if provided
        stepType: createStepDto.stepType || '3d-model', // Default to 3d-model
        videoUrl: createStepDto.videoUrl,
        imageUrl: createStepDto.imageUrl,
        // Add support for annotations
        imageAnnotations: createStepDto.imageAnnotations || [],
        videoAnnotations: createStepDto.videoAnnotations || [],
        autoPlay: createStepDto.autoPlay,
        duration: createStepDto.duration,
        tags: createStepDto.tags,
        metadata: createStepDto.metadata,
        createdAt: new Date(),
        updatedAt: new Date(),
        moduleId, // Link back to module
      });

      // Create step document
      const stepRef = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .add(stepData);

      // Update module's totalSteps count
      const currentStepsCount = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .get();

      await moduleRef.update({
        totalSteps: currentStepsCount.size,
        updatedAt: new Date(),
      });

      console.log(
        `✅ Admin created step: ${stepRef.id} for module: ${moduleId}`,
      );

      return {
        id: stepRef.id,
        ...stepData,
      };
    } catch (error: any) {
      console.error('Error creating step (admin):', error);
      throw new Error(
        `Failed to create step: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Update a step
   * @param moduleId - Module ID
   * @param stepId - Step ID
   * @param updateStepDto - Update data
   * @returns Updated step
   */
  async updateStepAdmin(
    moduleId: string,
    stepId: string,
    updateStepDto: UpdateStepDto,
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Check if step exists
      const stepRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .doc(stepId);

      const stepDoc = await stepRef.get();

      if (!stepDoc.exists) {
        throw new Error('Step not found');
      }

      // Prepare update data
      const updateData = {
        ...updateStepDto,
        updatedAt: new Date(),
      };

      // Update step
      await stepRef.update(updateData);

      // Get updated document
      const updatedDoc = await stepRef.get();

      console.log(`✅ Admin updated step: ${stepId} in module: ${moduleId}`);

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    } catch (error: any) {
      console.error('Error updating step (admin):', error);
      throw new Error(
        `Failed to update step: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Update step order
   * @param moduleId - Module ID
   * @param stepId - Step ID
   * @param newOrder - New order position
   * @returns Updated step
   */
  async updateStepOrderAdmin(
    moduleId: string,
    stepId: string,
    newOrder: number,
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Check if step exists
      const stepRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .doc(stepId);

      const stepDoc = await stepRef.get();

      if (!stepDoc.exists) {
        throw new Error('Step not found');
      }

      // Update step order
      await stepRef.update({
        order: newOrder,
        updatedAt: new Date(),
      });

      // Get updated document
      const updatedDoc = await stepRef.get();

      console.log(
        `✅ Admin updated step order: ${stepId} to ${newOrder} in module: ${moduleId}`,
      );

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    } catch (error: any) {
      console.error('Error updating step order (admin):', error);
      throw new Error(
        `Failed to update step order: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Delete a step
   * @param moduleId - Module ID
   * @param stepId - Step ID
   * @returns Success result with step data for cleanup
   */
  async deleteStepAdmin(moduleId: string, stepId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Check if step exists and get its data for file cleanup
      const stepRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .doc(stepId);

      const stepDoc = await stepRef.get();

      if (!stepDoc.exists) {
        throw new Error('Step not found');
      }

      // Get step data before deletion for file cleanup
      const stepData = stepDoc.data();

      // Delete step
      await stepRef.delete();

      // Update module's totalSteps count
      const moduleRef = db.collection('modules').doc(moduleId);
      const remainingStepsCount = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .get();

      await moduleRef.update({
        totalSteps: remainingStepsCount.size,
        updatedAt: new Date(),
      });

      console.log(`✅ Admin deleted step: ${stepId} from module: ${moduleId}`);

      return {
        success: true,
        id: stepId,
        moduleId,
        stepData, // Return step data for file cleanup
      };
    } catch (error: any) {
      console.error('Error deleting step (admin):', error);
      throw new Error(
        `Failed to delete step: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Get all steps for a module
   * @param moduleId - Module ID
   * @returns Array of steps
   */
  async getStepsAdmin(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();

      const stepsSnapshot = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .orderBy('order', 'asc')
        .get();

      const steps = stepsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || null,
        updatedAt: doc.data().updatedAt?.toDate?.() || null,
      }));

      return steps;
    } catch (error: any) {
      console.error('Error getting steps (admin):', error);
      throw new Error(
        `Failed to get steps: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Create a new quiz question for a module
   * @param moduleId - Module ID
   * @param createQuizDto - Quiz question data
   * @returns Created quiz question
   */
  async createQuizQuestionAdmin(
    moduleId: string,
    createQuizDto: CreateQuizQuestionDto,
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Check if module exists
      const moduleRef = db.collection('modules').doc(moduleId);
      const moduleDoc = await moduleRef.get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      // Generate order if not provided
      let finalOrder = createQuizDto.order;
      if (!finalOrder) {
        const lastQuiz = await db
          .collection('modules')
          .doc(moduleId)
          .collection('quiz')
          .orderBy('order', 'desc')
          .limit(1)
          .get();

        finalOrder = lastQuiz.empty ? 1 : lastQuiz.docs[0].data().order + 1;
      }

      // Helper function to remove undefined values
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) return null;
        if (typeof obj !== 'object') return obj;
        if (Array.isArray(obj)) return obj.map(removeUndefinedValues);

        const cleaned: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (value !== undefined) {
            cleaned[key] = removeUndefinedValues(value);
          }
        }
        return cleaned;
      };

      // Prepare quiz data to match database structure
      const quizData = removeUndefinedValues({
        question: createQuizDto.question,
        order: finalOrder,
        createdAt: new Date(),
        updatedAt: new Date(),
        moduleId, // Link back to module
        // Options as array of strings (matching database structure)
        options: createQuizDto.options,
        correctAnswer: createQuizDto.correctAnswer,
        explanation: createQuizDto.explanation,
      });

      // Create quiz question document
      const quizRef = await db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .add(quizData);

      // Update module's totalQuizQuestions count
      const currentQuizCount = await db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .get();

      await moduleRef.update({
        totalQuizQuestions: currentQuizCount.size,
        updatedAt: new Date(),
      });

      console.log(
        `✅ Admin created quiz question: ${quizRef.id} for module: ${moduleId}`,
      );

      return {
        id: quizRef.id,
        ...quizData,
      };
    } catch (error: any) {
      console.error('Error creating quiz question (admin):', error);
      throw new Error(
        `Failed to create quiz question: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Update a quiz question
   * @param moduleId - Module ID
   * @param quizId - Quiz question ID
   * @param updateQuizDto - Update data
   * @returns Updated quiz question
   */
  async updateQuizQuestionAdmin(
    moduleId: string,
    quizId: string,
    updateQuizDto: UpdateQuizQuestionDto,
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Check if quiz question exists
      const quizRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .doc(quizId);

      const quizDoc = await quizRef.get();

      if (!quizDoc.exists) {
        throw new Error('Quiz question not found');
      }

      // Helper function to remove undefined values
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) return null;
        if (typeof obj !== 'object') return obj;
        if (Array.isArray(obj)) return obj.map(removeUndefinedValues);

        const cleaned: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (value !== undefined) {
            cleaned[key] = removeUndefinedValues(value);
          }
        }
        return cleaned;
      };

      // Prepare update data to match database structure
      const updateData = removeUndefinedValues({
        ...updateQuizDto,
        updatedAt: new Date(),
        // Options as array of strings (matching database structure)
        ...(updateQuizDto.options && {
          options: updateQuizDto.options,
        }),
      });

      // Update quiz question
      await quizRef.update(updateData);

      // Get updated document
      const updatedDoc = await quizRef.get();

      console.log(
        `✅ Admin updated quiz question: ${quizId} in module: ${moduleId}`,
      );

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    } catch (error: any) {
      console.error('Error updating quiz question (admin):', error);
      throw new Error(
        `Failed to update quiz question: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Delete a quiz question
   * @param moduleId - Module ID
   * @param quizId - Quiz question ID
   * @returns Success result
   */
  async deleteQuizQuestionAdmin(moduleId: string, quizId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Check if quiz question exists
      const quizRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .doc(quizId);

      const quizDoc = await quizRef.get();

      if (!quizDoc.exists) {
        throw new Error('Quiz question not found');
      }

      // Delete quiz question
      await quizRef.delete();

      // Update module's totalQuizQuestions count
      const moduleRef = db.collection('modules').doc(moduleId);
      const remainingQuizCount = await db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .get();

      await moduleRef.update({
        totalQuizQuestions: remainingQuizCount.size,
        updatedAt: new Date(),
      });

      console.log(
        `✅ Admin deleted quiz question: ${quizId} from module: ${moduleId}`,
      );

      return {
        success: true,
        id: quizId,
        moduleId,
      };
    } catch (error: any) {
      console.error('Error deleting quiz question (admin):', error);
      throw new Error(
        `Failed to delete quiz question: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Get all quiz questions for a module
   * @param moduleId - Module ID
   * @returns Array of quiz questions
   */
  async getQuizQuestionsAdmin(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();

      const quizSnapshot = await db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .orderBy('order', 'asc')
        .get();

      const quizQuestions = quizSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || null,
        updatedAt: doc.data().updatedAt?.toDate?.() || null,
      }));

      return quizQuestions;
    } catch (error: any) {
      console.error('Error getting quiz questions (admin):', error);
      throw new Error(
        `Failed to get quiz questions: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Get courses
   */
  async getCourses() {
    try {
      const db = this.firebaseService.getDb();
      const coursesSnapshot = await db.collection('courses').get();

      const courses = coursesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || null,
        lastUpdated: doc.data().lastUpdated?.toDate?.() || null,
      }));

      return courses;
    } catch (error: any) {
      console.error('Error getting courses:', error);
      throw new Error(
        `Failed to get courses: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Get single step by ID
   * @param moduleId - Module ID
   * @param stepId - Step ID
   * @returns Step data
   */
  async getStepAdmin(moduleId: string, stepId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get step document
      const stepRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .doc(stepId);

      const stepDoc = await stepRef.get();

      if (!stepDoc.exists) {
        throw new Error('Step not found');
      }

      return {
        id: stepDoc.id,
        ...stepDoc.data(),
        createdAt: stepDoc.data()?.createdAt?.toDate?.() || null,
        updatedAt: stepDoc.data()?.updatedAt?.toDate?.() || null,
      };
    } catch (error: any) {
      console.error('Error getting step (admin):', error);
      throw new Error(
        `Failed to get step: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * [ADMIN] Update quiz question order
   * @param moduleId - Module ID
   * @param quizId - Quiz question ID
   * @param newOrder - New order position
   * @returns Updated quiz question
   */
  async updateQuizOrderAdmin(
    moduleId: string,
    quizId: string,
    newOrder: number,
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Check if quiz question exists
      const quizRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .doc(quizId);

      const quizDoc = await quizRef.get();

      if (!quizDoc.exists) {
        throw new Error('Quiz question not found');
      }

      // Update quiz question order
      await quizRef.update({
        order: newOrder,
        updatedAt: new Date(),
      });

      // Get updated document
      const updatedDoc = await quizRef.get();

      console.log(
        `✅ Admin updated quiz order: ${quizId} to order ${newOrder} in module: ${moduleId}`,
      );

      return {
        id: updatedDoc.id,
        ...updatedDoc.data(),
      };
    } catch (error: any) {
      console.error('Error updating quiz order (admin):', error);
      throw new Error(
        `Failed to update quiz order: ${error?.message || 'Unknown error'}`,
      );
    }
  }
}
