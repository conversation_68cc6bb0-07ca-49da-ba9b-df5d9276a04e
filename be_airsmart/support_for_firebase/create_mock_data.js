const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccount = require('../src/common/firebase/serviceAccountKey.json');

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

// Helper function to create step with full structure
function createStep(order, title, content, cameraPos = [0, 1, 5], hotspots = []) {
    const defaultHotspot = {
        id: `hotspot-${order}`,
        position: [0, 0, 0],
        label: title,
        action: "highlight",
        description: content.substring(0, 100) + "..."
    };

    return {
        order,
        title,
        content,
        camera: {
            position: cameraPos,
            target: [0, 0, 0]
        },
        hotspots: hotspots.length > 0 ? hotspots : [defaultHotspot]
    };
}

// Course metadata
const courseMetadata = {
    'airsmart-ies': {
        id: 'airsmart-ies',
        name: 'AirSmart IES',
        description: 'Complete AirSmart installation training program covering all aspects from safety to commissioning',
        thumbnail: 'https://airsmart.com/airsmart-cms/wp-content/uploads/2018/04/INFORM_LucasSt11575_R.jpg',
        tags: ['airsmart', 'installation', 'training', 'complete-course'],
        isLocked: false,
        isComingSoon: false,
        order: 1
    },
    'airsmart-spark': {
        id: 'airsmart-spark',
        name: 'AirSmart Spark - Coming soon',
        description: 'Advanced AirSmart features and smart home integration. This course is currently under development and will be available soon.',
        thumbnail: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        tags: ['airsmart', 'smart-home', 'advanced', 'coming-soon'],
        isLocked: true,
        isComingSoon: true,
        order: 2
    }
};

// Module-courses mapping
const moduleCourses = {
    1: { course: 'airsmart-ies', courseOrder: 1 },
    2: { course: 'airsmart-ies', courseOrder: 2 },
    3: { course: 'airsmart-ies', courseOrder: 3 },
    4: { course: 'airsmart-ies', courseOrder: 4 },
    5: { course: 'airsmart-ies', courseOrder: 5 },
    6: { course: 'airsmart-ies', courseOrder: 6 },
    7: { course: 'airsmart-ies', courseOrder: 7 },
    8: { course: 'airsmart-ies', courseOrder: 8 },
    9: { course: 'airsmart-ies', courseOrder: 9 },
    10: { course: 'airsmart-ies', courseOrder: 10 },
    11: { course: 'airsmart-ies', courseOrder: 11 },
    12: { course: 'airsmart-ies', courseOrder: 12 },
    13: { course: 'airsmart-ies', courseOrder: 13 }
};

// Modules 1-3 data (full structure, including video/image fields)
const modulesData = [
    {
        order: 1,
        name: "Safety & Pre-Installation Checks",
        description: "Guide to installing and configuring the AirSmart system",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_1.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["safety", "installation"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 4,
        steps: [
            {
                order: 1,
                title: "Review Safety Precautions",
                content: "Read all WARNINGS & CAUTIONS in the installation manual. Ensure you understand all safety requirements before proceeding.",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "safety-manual", position: [0, 0, 0], label: "Safety Manual", action: "highlight", description: "Review all safety warnings and cautions in the manual" }
                ],
                videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
                introjsSteps: [
                    { id: "intro1", intro: "Review all safety precautions", element: "#safety-manual", position: "top" }
                ]
            },
            {
                order: 2,
                title: "Verify Installer Certification",
                content: "Confirm installer certification and PPE requirements. Ensure all team members have proper training and safety equipment.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "certification", position: [0.5, 0, 0], label: "Certification", action: "highlight", description: "Verify installer certification and training requirements" }
                ],
                imageUrl: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro2", intro: "Verify all team certifications and PPE", element: "#certification", position: "top" }
                ]
            },
            {
                order: 3,
                title: "Confirm Site Conditions",
                content: "Check site conditions including no combustible gases and adequate ventilation. Verify the installation environment is safe.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "site-conditions", position: [-0.5, 0, 0], label: "Site Conditions", action: "highlight", description: "Check for combustible gases and ventilation" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.012 } }
                ],
                introjsSteps: [
                    { id: "intro3", intro: "Confirm safe site conditions", element: "#site-conditions", position: "right" }
                ]
            },
            {
                order: 4,
                title: "Identify Optimal Locations",
                content: "Identify optimal indoor/outdoor unit locations for service access and noise control. Plan the installation layout carefully.",
                camera: { position: [0, 2, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "indoor-location", position: [-1, 0, 0], label: "Indoor Unit Location", action: "highlight", description: "Optimal indoor unit placement" },
                    { id: "outdoor-location", position: [1, 0, 0], label: "Outdoor Unit Location", action: "highlight", description: "Optimal outdoor unit placement" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.005 } }
                ],
                introjsSteps: [
                    { id: "intro4", intro: "Plan optimal unit locations", element: "#indoor-location", position: "bottom" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What minimum clearance is required around the FCU for servicing?",
                options: ["300mm", "600mm", "900mm", "1200mm"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The FCU requires 600mm clearance for proper servicing access."
            },
            {
                order: 2,
                question: "Which safety measures are required when installing AirSmart systems? (Select all that apply)",
                options: ["Safety glasses", "Work gloves", "Proper ventilation", "Fire extinguisher nearby"],
                correctAnswer: [0, 1, 2],
                questionType: "multiple",
                explanation: "Safety glasses, work gloves, and proper ventilation are all required safety measures during installation."
            },
            {
                order: 3,
                question: "What are the essential PPE items for AirSmart installation? (Select all that apply)",
                options: ["Safety glasses", "Hard hat", "Steel-toe boots", "Casual clothing"],
                correctAnswer: [0, 1, 2],
                questionType: "multiple",
                explanation: "Essential PPE includes safety glasses, hard hat, and steel-toe boots. Casual clothing is not appropriate for installation work."
            }
        ]
    },
    {
        order: 2,
        name: "System Overview & Components",
        description: "Understanding AirSmart system components and layouts",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_2.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["components", "overview"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "IES System Layouts",
                content: "Explain IES-500, IES-700, IES-1250 typical layouts and dimensions. Understand the differences between each model.",
                camera: { position: [2, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "ies-500", position: [-1, 0, 0], label: "IES-500", action: "highlight", description: "Compact system for smaller installations" },
                    { id: "ies-1250", position: [1, 0, 0], label: "IES-1250", action: "highlight", description: "High-capacity system for larger installations" }
                ],
                videoUrl: "https://www.youtube.com/embed/jNQXAC9IVRw",
                introjsSteps: [
                    { id: "intro1", intro: "Compare different IES system layouts", element: "#ies-500", position: "top" }
                ]
            },
            {
                order: 2,
                title: "Main Components",
                content: "Identify indoor unit (FCU), outdoor unit (CDU), inverter interface, ductwork, and controls. Understand how each component functions.",
                camera: { position: [0, 2, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "fcu", position: [0, 1, 0], label: "FCU (Indoor Unit)", action: "highlight", description: "Fan coil unit for indoor air handling" },
                    { id: "cdu", position: [0, -1, 0], label: "CDU (Outdoor Unit)", action: "highlight", description: "Condensing unit for heat exchange" },
                    { id: "inverter", position: [1, 0, 0], label: "Inverter Interface", action: "highlight", description: "Communication bridge between components" }
                ],
                imageUrl: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro2", intro: "Identify the main system components", element: "#fcu", position: "bottom" }
                ]
            },
            {
                order: 3,
                title: "System Accessories",
                content: "Walk through accessories: filters, UV lamps, zone boards, grilles. Learn installation and maintenance requirements.",
                camera: { position: [1, 1, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "filters", position: [-0.5, 0, 0], label: "Air Filters", action: "highlight", description: "Replaceable air filtration components" },
                    { id: "uv-lamps", position: [0.5, 0, 0], label: "UV Lamps", action: "highlight", description: "Air purification UV lighting system" },
                    { id: "zone-boards", position: [0, 0.5, 0], label: "Zone Boards", action: "highlight", description: "Zoning control components" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro3", intro: "Learn about system accessories", element: "#filters", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What are the main differences between IES-500 and IES-1250?",
                options: ["Size only", "Capacity and dimensions", "Color options", "Installation method"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "IES-500 and IES-1250 differ primarily in capacity and physical dimensions."
            },
            {
                order: 2,
                question: "Which components are part of the main AirSmart system? (Select all that apply)",
                options: ["FCU (Indoor Unit)", "CDU (Outdoor Unit)", "Inverter Interface", "External water heater"],
                correctAnswer: [0, 1, 2],
                questionType: "multiple",
                explanation: "The main AirSmart system includes FCU, CDU, and Inverter Interface. External water heater is not a standard component."
            },
            {
                order: 3,
                question: "Which accessories enhance the AirSmart system functionality? (Select all that apply)",
                options: ["Air filters", "UV lamps", "Zone boards", "External speakers"],
                correctAnswer: [0, 1, 2],
                questionType: "multiple",
                explanation: "Air filters, UV lamps, and zone boards are all functional accessories for the AirSmart system."
            }
        ]
    },
    {
        order: 3,
        name: "Installation Workflow (High-Level)",
        description: "High-level installation workflow and sequencing",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_3.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["workflow", "installation"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 1,
        steps: [
            {
                order: 1,
                title: "Installation Order",
                content: "Follow the Installation Order: 1A–1G Indoor Unit → 2A–2E Outdoor Unit → 3A–3D Controls & Commissioning. This sequence ensures proper testing and commissioning.",
                camera: { position: [0, 1.5, 6], target: [0, 0, 0] },
                hotspots: [
                    { id: "indoor-phase", position: [-2, 0, 0], label: "1. Indoor Unit", action: "highlight", description: "Phase 1A-1G: Indoor unit installation" },
                    { id: "outdoor-phase", position: [0, 0, 0], label: "2. Outdoor Unit", action: "highlight", description: "Phase 2A-2E: Outdoor unit installation" },
                    { id: "controls-phase", position: [2, 0, 0], label: "3. Controls & Commissioning", action: "highlight", description: "Phase 3A-3D: Controls and commissioning" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.006 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Follow the correct installation sequence", element: "#indoor-phase", position: "bottom" },
                    { id: "intro2", intro: "Then proceed to outdoor unit", element: "#outdoor-phase", position: "bottom" },
                    { id: "intro3", intro: "Finally, controls and commissioning", element: "#controls-phase", position: "bottom" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What is the correct order of installation phases?",
                options: ["Controls, Outdoor, Indoor", "Indoor, Outdoor, Controls", "Outdoor, Indoor, Controls", "Any order is fine"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The correct order is Indoor, Outdoor, Controls for proper testing and commissioning."
            },
            {
                order: 2,
                question: "What factors make installation order important? (Select all that apply)",
                options: ["Safety requirements", "Proper commissioning sequence", "Cost efficiency", "Weather conditions"],
                correctAnswer: [0, 1],
                questionType: "multiple",
                explanation: "Installation order is important for safety requirements and proper commissioning sequence."
            },
            {
                order: 3,
                question: "Which phase includes commissioning checks?",
                options: ["Indoor installation", "Outdoor installation", "Controls & commissioning", "Pre-installation"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "Commissioning checks are performed during the Controls & Commissioning phase."
            }
        ]
    },
    {
        order: 4,
        name: "Indoor Unit Installation",
        description: "Complete guide to indoor unit installation procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_4.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["indoor", "installation"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 6,
        steps: [
            {
                order: 1,
                title: "Location & Orientation",
                content: "Location & Orientation (vertical, horizontal, split). Determine proper location and orientation for the indoor unit based on site requirements.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "vertical-mount", position: [-1, 0, 0], label: "Vertical Mount", action: "highlight", description: "Vertical mounting option for indoor unit" },
                    { id: "horizontal-mount", position: [0, 0, 0], label: "Horizontal Mount", action: "highlight", description: "Horizontal mounting option for indoor unit" },
                    { id: "split-mount", position: [1, 0, 0], label: "Split Mount", action: "highlight", description: "Split mounting option for indoor unit" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Choose the appropriate mounting orientation", element: "#vertical-mount", position: "top" }
                ]
            },
            {
                order: 2,
                title: "Clearances",
                content: "Clearances: maintain service space (e.g. 600 mm around FCU-500). Ensure proper maintenance access around the unit.",
                camera: { position: [0, 2, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "clearance-600mm", position: [0, 0, 0], label: "600mm Clearance", action: "highlight", description: "Maintain 600mm clearance around FCU-500 for servicing" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro2", intro: "Ensure proper clearances for maintenance", element: "#clearance-600mm", position: "bottom" }
                ]
            },
            {
                order: 3,
                title: "Mounting",
                content: "Mounting: secure to structure, install drip pan & P-trap. Properly mount the unit and install drainage components.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "mounting-bracket", position: [-0.5, 0, 0], label: "Mounting Bracket", action: "highlight", description: "Secure mounting bracket to structure" },
                    { id: "drip-pan", position: [0.5, 0, 0], label: "Drip Pan & P-trap", action: "highlight", description: "Install drip pan and P-trap for drainage" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.012 } }
                ],
                introjsSteps: [
                    { id: "intro3", intro: "Secure mounting and install drainage", element: "#mounting-bracket", position: "right" }
                ]
            },
            {
                order: 4,
                title: "Coil & Piping",
                content: "Coil & Piping: install RFC or HCW coil in housing, seal & sensor wiring. Complete coil installation and sensor connections.",
                camera: { position: [1, 1, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "rfc-coil", position: [-0.5, 0, 0], label: "RFC Coil", action: "highlight", description: "Refrigerant coil installation" },
                    { id: "sensor-wiring", position: [0.5, 0, 0], label: "Sensor Wiring", action: "highlight", description: "Temperature sensor wiring connections" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro4", intro: "Install coil and complete sensor wiring", element: "#rfc-coil", position: "bottom" }
                ]
            },
            {
                order: 5,
                title: "Drain Pan",
                content: "Drain Pan: fit secondary drain pan and test flow. Install secondary drainage and verify proper water flow.",
                camera: { position: [0, 0, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "secondary-drain", position: [0, 0, 0], label: "Secondary Drain Pan", action: "highlight", description: "Secondary drain pan for backup drainage" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.007 } }
                ],
                introjsSteps: [
                    { id: "intro5", intro: "Test secondary drain pan flow", element: "#secondary-drain", position: "top" }
                ]
            },
            {
                order: 6,
                title: "Air Purifier (Optional)",
                content: "Air Purifier (optional): attach purification module, test UV lamps. Install optional air purification components.",
                camera: { position: [0, 1, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "uv-purifier", position: [0, 0, 0], label: "UV Air Purifier", action: "highlight", description: "Optional UV air purification module" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.011 } }
                ],
                introjsSteps: [
                    { id: "intro6", intro: "Install and test UV purification module", element: "#uv-purifier", position: "bottom" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What are the three mounting options for indoor units?",
                options: ["Vertical, horizontal, split", "Wall, ceiling, floor", "Left, right, center", "High, medium, low"],
                correctAnswer: [0],
                questionType: "single",
                explanation: "Indoor units can be mounted in vertical, horizontal, or split configurations."
            },
            {
                order: 2,
                question: "What component prevents water leaks and must be tested with water?",
                options: ["P-trap", "Drain pan", "Coil housing", "Filter"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The drain pan prevents water leaks and must be tested with water to ensure proper drainage."
            },
            {
                order: 3,
                question: "When installing a HCW coil, where is the sensor placed?",
                options: ["In the housing", "On the coil", "In the ductwork", "On the control panel"],
                correctAnswer: [0],
                questionType: "single",
                explanation: "When installing a HCW coil, the sensor is placed in the housing for proper temperature monitoring."
            }
        ]
    },
    {
        order: 5,
        name: "Ductwork & Grilles",
        description: "Ductwork installation and grille placement procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_5.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["ductwork", "grilles"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 5,
        steps: [
            createStep(1, "Primary Duct Selection", "Primary Duct Selection: choose diameter from Smartflow table. Select appropriate duct diameter based on system requirements.", [0, 1, 5]),
            createStep(2, "Spigot Installation", "Spigot Installation: cut hole, heat-weld spigot, tape seams. Install spigot connections securely.", [1, 0, 4]),
            createStep(3, "Flexible Duct", "Flexible Duct: install with sweeping bends, minimum 6 m runs. Ensure optimal airflow with proper duct routing.", [-1, 1, 5]),
            createStep(4, "Grille Placement", "Grille Placement: mark 200 mm from walls, avoid high-traffic/obstruction areas. Position grilles for optimal air distribution.", [0, 2, 3]),
            createStep(5, "Connection & Seal", "Connection & Seal: secure grilles with spring clips, ensure airtight seal. Complete installation with proper sealing.", [1, 1, 3])
        ],
        quiz: [
            {
                order: 1,
                question: "What is the minimum duct length recommended for performance?",
                options: ["3 meters", "6 meters", "9 meters", "12 meters"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The minimum duct length recommended for optimal performance is 6 meters."
            },
            {
                order: 2,
                question: "How far from walls should grilles be installed?",
                options: ["100mm", "200mm", "300mm", "400mm"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Grilles should be installed 200mm from walls for proper airflow distribution."
            },
            {
                order: 3,
                question: "What is the purpose of spring clips in grille installation?",
                options: ["Decoration", "Secure mounting", "Airflow control", "Noise reduction"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Spring clips are used to securely mount grilles and ensure proper installation."
            }
        ]
    },
    {
        order: 6,
        name: "Outdoor Unit Installation",
        description: "Outdoor unit positioning, connection and evacuation procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_6.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["outdoor", "installation"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 4,
        steps: [
            {
                order: 1,
                title: "Positioning & Mounting",
                content: "Position on vibration-isolated pad or slab, maintain clearances (≥30 cm walls). Anchor with M10 bolts.",
                camera: { position: [2, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "vibration-pad", position: [0, -0.5, 0], label: "Vibration Pad", action: "highlight", description: "Position unit on vibration-isolated pad" },
                    { id: "clearance-30cm", position: [1.5, 0, 0], label: "30cm Clearance", action: "highlight", description: "Maintain 30cm clearance from walls" },
                    { id: "m10-bolts", position: [0, 0, 0], label: "M10 Bolts", action: "highlight", description: "Anchor unit with M10 bolts" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Position unit on vibration-isolated pad", element: "#vibration-pad", position: "bottom" },
                    { id: "intro2", intro: "Ensure 30cm clearance from walls", element: "#clearance-30cm", position: "right" },
                    { id: "intro3", intro: "Secure with M10 bolts", element: "#m10-bolts", position: "top" }
                ]
            },
            {
                order: 2,
                title: "Deflectors & Protection",
                content: "Anchor with M10 bolts; fit deflectors as needed. Install weather protection and deflectors.",
                camera: { position: [0, 2, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "deflectors", position: [0.5, 0, 0], label: "Deflectors", action: "highlight", description: "Install weather deflectors" },
                    { id: "weather-protection", position: [-0.5, 0, 0], label: "Weather Protection", action: "highlight", description: "Add weather protection components" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Install weather deflectors", element: "#deflectors", position: "left" },
                    { id: "intro2", intro: "Add weather protection", element: "#weather-protection", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Refrigerant Piping",
                content: "Connect refrigerant piping: flare nut torque values, insulation, UV protection. Complete refrigerant connections.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "flare-nuts", position: [-0.5, 0, 0], label: "Flare Nuts", action: "highlight", description: "Connect with proper torque values" },
                    { id: "insulation", position: [0.5, 0, 0], label: "UV Insulation", action: "highlight", description: "Apply UV-resistant insulation" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.012 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Connect refrigerant piping with proper torque", element: "#flare-nuts", position: "bottom" },
                    { id: "intro2", intro: "Apply UV-resistant insulation", element: "#insulation", position: "top" }
                ]
            },
            {
                order: 4,
                title: "Evacuation",
                content: "Evacuation: vacuum to –76 cmHg, check hold for 5 min, vent high-pressure port. Complete system evacuation.",
                camera: { position: [1, 0, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "vacuum-gauge", position: [0, 0, 0], label: "Vacuum Gauge", action: "highlight", description: "Monitor vacuum to -76 cmHg" },
                    { id: "high-pressure-port", position: [0.5, 0, 0], label: "High-Pressure Port", action: "highlight", description: "Vent high-pressure port" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.007 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Evacuate to -76 cmHg", element: "#vacuum-gauge", position: "bottom" },
                    { id: "intro2", intro: "Hold for 5 minutes", element: "#vacuum-gauge", position: "top" },
                    { id: "intro3", intro: "Vent high-pressure port", element: "#high-pressure-port", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What tool is required to verify proper refrigerant evacuation?",
                options: ["Pressure gauge", "Vacuum gauge", "Temperature probe", "Flow meter"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "A vacuum gauge is required to verify proper refrigerant evacuation to -76 cmHg."
            },
            {
                order: 2,
                question: "What is the minimum clearance from a wall for a CDU?",
                options: ["20 cm", "30 cm", "40 cm", "50 cm"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The minimum clearance from a wall for a CDU is 30 cm."
            },
            {
                order: 3,
                question: "Why is UV-resistant insulation required on piping?",
                options: ["Cost savings", "Weather protection", "Performance", "Appearance"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "UV-resistant insulation protects piping from weather damage and UV degradation."
            }
        ]
    },
    {
        order: 7,
        name: "Electrical & Controls Wiring",
        description: "Complete electrical wiring and controls configuration",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_7.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["electrical", "controls"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "Indoor Wiring",
                content: "Indoor Wiring: thermostat, PSB control, interface board connections. Complete indoor electrical connections.",
                camera: { position: [0, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "thermostat", position: [-0.5, 0, 0], label: "Thermostat", action: "highlight", description: "Connect thermostat wiring" },
                    { id: "psb-control", position: [0, 0, 0], label: "PSB Control", action: "highlight", description: "Install PSB control board" },
                    { id: "interface-board", position: [0.5, 0, 0], label: "Interface Board", action: "highlight", description: "Connect interface board" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Connect thermostat wiring", element: "#thermostat", position: "left" },
                    { id: "intro2", intro: "Install PSB control board", element: "#psb-control", position: "bottom" },
                    { id: "intro3", intro: "Connect interface board", element: "#interface-board", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Outdoor Wiring",
                content: "Outdoor Wiring: power supply spec, u-lug crimping, cable clamps, surge protection. Install outdoor electrical components.",
                camera: { position: [1, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "power-supply", position: [-0.5, 0, 0], label: "Power Supply", action: "highlight", description: "Connect power supply" },
                    { id: "u-lug-crimping", position: [0, 0, 0], label: "U-Lug Crimping", action: "highlight", description: "Crimp U-lug connections" },
                    { id: "surge-protection", position: [0.5, 0, 0], label: "Surge Protection", action: "highlight", description: "Install surge protection" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.011 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Connect power supply", element: "#power-supply", position: "left" },
                    { id: "intro2", intro: "Crimp U-lug connections", element: "#u-lug-crimping", position: "bottom" },
                    { id: "intro3", intro: "Install surge protection", element: "#surge-protection", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Zone Controls",
                content: "Zone Controls: install zone boards and actuators; configure DIP switches. Set up zoning control systems.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "zone-boards", position: [-0.5, 0, 0], label: "Zone Boards", action: "highlight", description: "Install zone control boards" },
                    { id: "actuators", position: [0, 0, 0], label: "Actuators", action: "highlight", description: "Connect zone actuators" },
                    { id: "dip-switches", position: [0.5, 0, 0], label: "DIP Switches", action: "highlight", description: "Configure DIP switch settings" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Install zone control boards", element: "#zone-boards", position: "left" },
                    { id: "intro2", intro: "Connect zone actuators", element: "#actuators", position: "bottom" },
                    { id: "intro3", intro: "Configure DIP switches", element: "#dip-switches", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What is the control signal between inverter and interface board?",
                options: ["24V AC", "12V DC", "Digital communication", "Analog voltage"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "The control signal between inverter and interface board is digital communication."
            },
            {
                order: 2,
                question: "Where should surge protection be installed?",
                options: ["Indoor unit only", "Outdoor unit only", "Both units", "Control panel only"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "Surge protection should be installed on both indoor and outdoor units."
            },
            {
                order: 3,
                question: "What settings are required for a zone with no damper?",
                options: ["DIP switch OFF", "DIP switch ON", "Special wiring", "No configuration needed"],
                correctAnswer: [0],
                questionType: "single",
                explanation: "For a zone with no damper, the DIP switch should be set to OFF."
            }
        ]
    },
    {
        order: 8,
        name: "Commissioning & Testing",
        description: "System commissioning and performance testing procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_8.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["commissioning", "testing"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "Pre-Run Checks",
                content: "Pre-Run Checks: leak test, drain test, airflow clearance, power correct. Perform all pre-commissioning checks.",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "leak-test", position: [-0.5, 0, 0], label: "Leak Test", action: "highlight", description: "Perform refrigerant leak test" },
                    { id: "drain-test", position: [0, 0, 0], label: "Drain Test", action: "highlight", description: "Test drainage system" },
                    { id: "airflow-clearance", position: [0.5, 0, 0], label: "Airflow Clearance", action: "highlight", description: "Check airflow clearances" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Perform refrigerant leak test", element: "#leak-test", position: "left" },
                    { id: "intro2", intro: "Test drainage system", element: "#drain-test", position: "bottom" },
                    { id: "intro3", intro: "Check airflow clearances", element: "#airflow-clearance", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Test Run",
                content: "Test Run: verify operation, record performance data. Execute system test run and document results.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "operation-verify", position: [-0.5, 0, 0], label: "Operation Verify", action: "highlight", description: "Verify system operation" },
                    { id: "performance-data", position: [0.5, 0, 0], label: "Performance Data", action: "highlight", description: "Record performance data" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Verify system operation", element: "#operation-verify", position: "left" },
                    { id: "intro2", intro: "Record performance data", element: "#performance-data", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Airflow Commissioning",
                content: "Airflow Commissioning: balance vents per design. Balance airflow according to design specifications.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "vent-balancing", position: [0, 0, 0], label: "Vent Balancing", action: "highlight", description: "Balance air vents" },
                    { id: "design-specs", position: [0.5, 0, 0], label: "Design Specs", action: "highlight", description: "Check design specifications" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Balance air vents per design", element: "#vent-balancing", position: "bottom" },
                    { id: "intro2", intro: "Verify design specifications", element: "#design-specs", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What is the vacuum level to confirm tight refrigerant piping?",
                options: ["-50 cmHg", "-76 cmHg", "-100 cmHg", "-120 cmHg"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "The vacuum level to confirm tight refrigerant piping is -76 cmHg."
            },
            {
                order: 2,
                question: "What must be checked before powering the unit for the first time?",
                options: ["Only electrical connections", "All connections and clearances", "Just the thermostat", "Only the outdoor unit"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "All connections and clearances must be checked before powering the unit for the first time."
            },
            {
                order: 3,
                question: "What documentation should be completed post-test?",
                options: ["Installation checklist only", "Performance data only", "Complete commissioning report", "Photos only"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "A complete commissioning report should be completed post-test for warranty and service records."
            }
        ]
    },
    {
        order: 9,
        name: "Air Purification & Optional Sources",
        description: "Installation of air purification and optional heating/cooling sources",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_9.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["purification", "optional"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "Hydronic Coil Installation",
                content: "Hydronic Coil Installation: slide HCW into blower, connect to water source. Install hydronic heating/cooling coil.",
                camera: { position: [0, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "hcw-coil", position: [-0.5, 0, 0], label: "HCW Coil", action: "highlight", description: "Slide HCW coil into blower" },
                    { id: "water-source", position: [0.5, 0, 0], label: "Water Source", action: "highlight", description: "Connect to water source" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Slide HCW coil into blower", element: "#hcw-coil", position: "left" },
                    { id: "intro2", intro: "Connect to water source", element: "#water-source", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Chilled Water Coil",
                content: "Chilled Water Coil: mount & pipe per spec, vent drain. Install and connect chilled water coil system.",
                camera: { position: [1, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "chilled-coil", position: [0, 0, 0], label: "Chilled Water Coil", action: "highlight", description: "Mount chilled water coil" },
                    { id: "vent-drain", position: [0.5, 0, 0], label: "Vent Drain", action: "highlight", description: "Vent and drain system" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Mount chilled water coil", element: "#chilled-coil", position: "bottom" },
                    { id: "intro2", intro: "Vent and drain system", element: "#vent-drain", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Purification Module",
                content: "Purification Module: replace filters, UV lamp maintenance. Install and maintain air purification components.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "filters", position: [-0.5, 0, 0], label: "Filters", action: "highlight", description: "Replace air filters" },
                    { id: "uv-lamps", position: [0.5, 0, 0], label: "UV Lamps", action: "highlight", description: "Maintain UV lamps" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Replace air filters", element: "#filters", position: "left" },
                    { id: "intro2", intro: "Maintain UV lamps", element: "#uv-lamps", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What safety concern applies when testing UV lamps?",
                options: ["Electric shock", "UV radiation exposure", "Fire hazard", "Chemical exposure"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "UV radiation exposure is the primary safety concern when testing UV lamps."
            },
            {
                order: 2,
                question: "How should water coils be vented after install?",
                options: ["No venting needed", "Top of coil", "Bottom of coil", "Both ends"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Water coils should be vented at the top of the coil to remove air pockets."
            },
            {
                order: 3,
                question: "What is the maintenance interval for air purification lamps?",
                options: ["Monthly", "Quarterly", "Annually", "As needed"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "Air purification lamps should be maintained annually or as specified by manufacturer."
            }
        ]
    },
    {
        order: 10,
        name: "Troubleshooting & Maintenance",
        description: "System troubleshooting and routine maintenance procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_10.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["troubleshooting", "maintenance"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "Indoor Unit Faults",
                content: "Indoor Unit Faults: motor speeds, board errors, sensor issues. Diagnose and resolve indoor unit problems.",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "motor-speeds", position: [-0.5, 0, 0], label: "Motor Speeds", action: "highlight", description: "Check motor speed settings" },
                    { id: "board-errors", position: [0, 0, 0], label: "Board Errors", action: "highlight", description: "Diagnose board errors" },
                    { id: "sensor-issues", position: [0.5, 0, 0], label: "Sensor Issues", action: "highlight", description: "Troubleshoot sensor problems" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Check motor speed settings", element: "#motor-speeds", position: "left" },
                    { id: "intro2", intro: "Diagnose board errors", element: "#board-errors", position: "bottom" },
                    { id: "intro3", intro: "Troubleshoot sensor problems", element: "#sensor-issues", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Outdoor Faults",
                content: "Outdoor Faults: refrigerant leaks, electrical malfunctions. Troubleshoot outdoor unit issues.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "refrigerant-leaks", position: [-0.5, 0, 0], label: "Refrigerant Leaks", action: "highlight", description: "Check for refrigerant leaks" },
                    { id: "electrical-malfunctions", position: [0.5, 0, 0], label: "Electrical Issues", action: "highlight", description: "Diagnose electrical problems" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Check for refrigerant leaks", element: "#refrigerant-leaks", position: "left" },
                    { id: "intro2", intro: "Diagnose electrical problems", element: "#electrical-malfunctions", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Routine Checks",
                content: "Routine Checks: annual leak test, filter changes, belt/coil inspection. Perform regular maintenance tasks.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "annual-leak-test", position: [-0.5, 0, 0], label: "Annual Leak Test", action: "highlight", description: "Perform annual leak testing" },
                    { id: "filter-changes", position: [0, 0, 0], label: "Filter Changes", action: "highlight", description: "Replace air filters" },
                    { id: "belt-coil-inspection", position: [0.5, 0, 0], label: "Belt/Coil Inspection", action: "highlight", description: "Inspect belts and coils" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Perform annual leak testing", element: "#annual-leak-test", position: "left" },
                    { id: "intro2", intro: "Replace air filters", element: "#filter-changes", position: "bottom" },
                    { id: "intro3", intro: "Inspect belts and coils", element: "#belt-coil-inspection", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What symptom might indicate reversed duct airflow?",
                options: ["High energy use", "Poor temperature control", "Unusual noise", "All of the above"],
                correctAnswer: [3],
                questionType: "single",
                explanation: "Reversed duct airflow can cause high energy use, poor temperature control, and unusual noise."
            },
            {
                order: 2,
                question: "How often should filters be checked or replaced?",
                options: ["Weekly", "Monthly", "Quarterly", "Annually"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "Filters should be checked or replaced quarterly for optimal system performance."
            },
            {
                order: 3,
                question: "What is the purpose of annual refrigerant leak testing?",
                options: ["Legal requirement", "System efficiency", "Environmental protection", "All of the above"],
                correctAnswer: [3],
                questionType: "single",
                explanation: "Annual refrigerant leak testing serves legal, efficiency, and environmental protection purposes."
            }
        ]
    },
    {
        order: 11,
        name: "Tools, Materials & Pre-Job Checklist",
        description: "Essential tools, materials and pre-installation preparation",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_11.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["tools", "materials"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 4,
        steps: [
            {
                order: 1,
                title: "Required Tools",
                content: "Introduce required tools for AirSmart installation (e.g., torque wrench, gauges, crimpers, insulation tools).",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "torque-wrench", position: [-0.5, 0, 0], label: "Torque Wrench", action: "highlight", description: "Essential torque wrench" },
                    { id: "gauges", position: [0, 0, 0], label: "Gauges", action: "highlight", description: "Pressure and vacuum gauges" },
                    { id: "crimpers", position: [0.5, 0, 0], label: "Crimpers", action: "highlight", description: "Crimping tools" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Essential torque wrench", element: "#torque-wrench", position: "left" },
                    { id: "intro2", intro: "Pressure and vacuum gauges", element: "#gauges", position: "bottom" },
                    { id: "intro3", intro: "Crimping tools", element: "#crimpers", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Consumables List",
                content: "List consumables: duct tape, insulation wrap, spigot collars, drain pipes, etc.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "duct-tape", position: [-0.5, 0, 0], label: "Duct Tape", action: "highlight", description: "Sealing duct tape" },
                    { id: "insulation-wrap", position: [0.5, 0, 0], label: "Insulation Wrap", action: "highlight", description: "Insulation materials" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Sealing duct tape", element: "#duct-tape", position: "left" },
                    { id: "intro2", intro: "Insulation materials", element: "#insulation-wrap", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Job Site Preparation",
                content: "Explain job site prep (what to verify before starting installation).",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "access-routes", position: [-0.5, 0, 0], label: "Access Routes", action: "highlight", description: "Check access routes" },
                    { id: "clearances", position: [0.5, 0, 0], label: "Clearances", action: "highlight", description: "Verify clearances" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Check access routes", element: "#access-routes", position: "left" },
                    { id: "intro2", intro: "Verify clearances", element: "#clearances", position: "right" }
                ]
            },
            {
                order: 4,
                title: "Interactive Checklist",
                content: "Interactive checklist simulation. Complete pre-installation checklist verification.",
                camera: { position: [0, 2, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "checklist", position: [0, 0, 0], label: "Pre-Installation Checklist", action: "highlight", description: "Complete checklist verification" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.007 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Complete pre-installation checklist", element: "#checklist", position: "bottom" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "Name three essential tools every AirSmart installer must have.",
                options: ["Torque wrench, gauges, crimpers", "Hammer, screwdriver, pliers", "Drill, saw, level", "Tape measure, pencil, calculator"],
                correctAnswer: [0],
                questionType: "single",
                explanation: "Essential tools include torque wrench, gauges, and crimpers for proper installation."
            },
            {
                order: 2,
                question: "What consumable is used for sealing duct joints?",
                options: ["Silicone", "Duct tape", "Foam", "Caulk"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Duct tape is the primary consumable used for sealing duct joints."
            },
            {
                order: 3,
                question: "What are two items that should be inspected on-site before unpacking equipment?",
                options: ["Weather and time", "Access and clearances", "Tools and materials", "Power and water"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Access routes and clearances should be inspected before unpacking equipment."
            }
        ]
    },
    {
        order: 12,
        name: "Documentation & Handover Process",
        description: "Installation documentation and customer handover procedures",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_12.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["documentation", "handover"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 4,
        steps: [
            {
                order: 1,
                title: "Installation Documentation",
                content: "How to document installation (photos, measurements, serial numbers). Record all installation details.",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "photos", position: [-0.5, 0, 0], label: "Photos", action: "highlight", description: "Take installation photos" },
                    { id: "measurements", position: [0, 0, 0], label: "Measurements", action: "highlight", description: "Record measurements" },
                    { id: "serial-numbers", position: [0.5, 0, 0], label: "Serial Numbers", action: "highlight", description: "Document serial numbers" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Take installation photos", element: "#photos", position: "left" },
                    { id: "intro2", intro: "Record measurements", element: "#measurements", position: "bottom" },
                    { id: "intro3", intro: "Document serial numbers", element: "#serial-numbers", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Required Paperwork",
                content: "What paperwork is required for job completion. Complete all necessary documentation.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "completion-forms", position: [-0.5, 0, 0], label: "Completion Forms", action: "highlight", description: "Fill completion forms" },
                    { id: "warranty-docs", position: [0.5, 0, 0], label: "Warranty Docs", action: "highlight", description: "Prepare warranty documents" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Fill completion forms", element: "#completion-forms", position: "left" },
                    { id: "intro2", intro: "Prepare warranty documents", element: "#warranty-docs", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Client Explanation",
                content: "How to explain the system to clients (homeowners/builders). Provide system operation training.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "operation-training", position: [-0.5, 0, 0], label: "Operation Training", action: "highlight", description: "Train on system operation" },
                    { id: "maintenance-guide", position: [0.5, 0, 0], label: "Maintenance Guide", action: "highlight", description: "Explain maintenance procedures" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Train on system operation", element: "#operation-training", position: "left" },
                    { id: "intro2", intro: "Explain maintenance procedures", element: "#maintenance-guide", position: "right" }
                ]
            },
            {
                order: 4,
                title: "Document Upload",
                content: "Uploading documents to CRM or warranty system. Submit completion documentation.",
                camera: { position: [0, 2, 3], target: [0, 0, 0] },
                hotspots: [
                    { id: "crm-upload", position: [0, 0, 0], label: "CRM Upload", action: "highlight", description: "Upload to CRM system" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.007 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Upload to CRM system", element: "#crm-upload", position: "bottom" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What must be recorded for warranty validation?",
                options: ["Installation date only", "Serial numbers and photos", "Customer signature only", "Payment receipt"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Serial numbers and photos must be recorded for warranty validation."
            },
            {
                order: 2,
                question: "What should be explained to the homeowner after install?",
                options: ["Nothing needed", "Basic operation and maintenance", "Technical specifications", "Repair procedures"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Basic operation and maintenance should be explained to the homeowner after installation."
            },
            {
                order: 3,
                question: "How is the job completion file submitted?",
                options: ["Email only", "Physical delivery", "Upload to system", "Phone call"],
                correctAnswer: [2],
                questionType: "single",
                explanation: "The job completion file is submitted by uploading to the designated system."
            }
        ]
    },
    {
        order: 13,
        name: "Common Errors & Callbacks",
        description: "Common installation mistakes and how to avoid service callbacks",
        thumbnail: "https://firebasestorage.googleapis.com/v0/b/airsmart-project.appspot.com/o/images%2Fmodule_13.jpg?alt=media",
        modelUrl: "https://s3.ap-southeast-2.wasabisys.com/airsmart/simple_box.obj",
        tags: ["errors", "callbacks"],
        isLocked: false,
        passingScore: 70,
        totalQuizQuestions: 3,
        totalSteps: 3,
        steps: [
            {
                order: 1,
                title: "Common Mistakes",
                content: "Showcase top 5–10 common mistakes (e.g., poor sealing, reversed airflow, incorrect wiring). Learn from common errors.",
                camera: { position: [0, 1, 5], target: [0, 0, 0] },
                hotspots: [
                    { id: "poor-sealing", position: [-0.5, 0, 0], label: "Poor Sealing", action: "highlight", description: "Avoid poor sealing practices" },
                    { id: "reversed-airflow", position: [0, 0, 0], label: "Reversed Airflow", action: "highlight", description: "Prevent reversed airflow" },
                    { id: "incorrect-wiring", position: [0.5, 0, 0], label: "Incorrect Wiring", action: "highlight", description: "Avoid wiring mistakes" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.008 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Avoid poor sealing practices", element: "#poor-sealing", position: "left" },
                    { id: "intro2", intro: "Prevent reversed airflow", element: "#reversed-airflow", position: "bottom" },
                    { id: "intro3", intro: "Avoid wiring mistakes", element: "#incorrect-wiring", position: "right" }
                ]
            },
            {
                order: 2,
                title: "Visual Simulations",
                content: "Visual simulations of wrong vs. correct installations. Compare proper and improper techniques.",
                camera: { position: [1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "wrong-installation", position: [-0.5, 0, 0], label: "Wrong Installation", action: "highlight", description: "Examples of wrong installation" },
                    { id: "correct-installation", position: [0.5, 0, 0], label: "Correct Installation", action: "highlight", description: "Examples of correct installation" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.01 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Examples of wrong installation", element: "#wrong-installation", position: "left" },
                    { id: "intro2", intro: "Examples of correct installation", element: "#correct-installation", position: "right" }
                ]
            },
            {
                order: 3,
                title: "Avoiding Callbacks",
                content: "How to avoid service callouts post-install. Prevent future service issues through proper installation.",
                camera: { position: [-1, 1, 4], target: [0, 0, 0] },
                hotspots: [
                    { id: "prevention-tips", position: [-0.5, 0, 0], label: "Prevention Tips", action: "highlight", description: "Tips to prevent callbacks" },
                    { id: "quality-checks", position: [0.5, 0, 0], label: "Quality Checks", action: "highlight", description: "Final quality checks" }
                ],
                actions: [
                    { type: "rotate", params: { axis: [0, 1, 0], speed: 0.009 } }
                ],
                introjsSteps: [
                    { id: "intro1", intro: "Tips to prevent callbacks", element: "#prevention-tips", position: "left" },
                    { id: "intro2", intro: "Final quality checks", element: "#quality-checks", position: "right" }
                ]
            }
        ],
        quiz: [
            {
                order: 1,
                question: "What's the risk of routing flexible duct too tightly?",
                options: ["No risk", "Reduced airflow", "Increased efficiency", "Better appearance"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "Routing flexible duct too tightly reduces airflow and system efficiency."
            },
            {
                order: 2,
                question: "What problem can occur if the drain pan isn't sloped correctly?",
                options: ["No drainage", "Water backup", "Overflow", "All of the above"],
                correctAnswer: [3],
                questionType: "single",
                explanation: "Incorrect drain pan slope can cause no drainage, water backup, and overflow issues."
            },
            {
                order: 3,
                question: "Why should DIP switch settings be reviewed before commissioning?",
                options: ["Not important", "Prevents system malfunction", "Required by code", "Customer preference"],
                correctAnswer: [1],
                questionType: "single",
                explanation: "DIP switch settings should be reviewed to prevent system malfunction during commissioning."
            }
        ]
    }
];

// Main function to create mock database
async function createMockDatabase() {
    try {
        console.log('🚀 Starting to create mock database...');

        // 1. Create courses collection
        console.log('\n📚 Creating courses collection...');
        for (const [courseId, courseData] of Object.entries(courseMetadata)) {
            const courseRef = db.collection('courses').doc(courseId);

            const courseWithTimestamps = {
                ...courseData,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            };

            await courseRef.set(courseWithTimestamps);
            console.log(`✅ Course created: ${courseId}`);
        }

        // 2. Create modules collection with course mapping
        console.log('\n📖 Creating modules collection...');
        for (const moduleData of modulesData) {
            console.log(`Adding Module ${moduleData.order}: ${moduleData.name}`);

            // Create module document
            const moduleRef = db.collection('modules').doc(`module${moduleData.order}`);

            // Add course mapping
            const { steps, quiz, ...moduleInfo } = moduleData;
            const moduleWithTimestamps = {
                ...moduleInfo,
                course: moduleCourses[moduleData.order].course,
                courseOrder: moduleCourses[moduleData.order].courseOrder,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            };
            await moduleRef.set(moduleWithTimestamps);

            // Add steps subcollection
            console.log(`  Adding ${steps.length} steps...`);
            for (const step of steps) {
                const stepWithTimestamps = {
                    ...step,
                    createdAt: admin.firestore.FieldValue.serverTimestamp(),
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                };
                await moduleRef.collection('steps').doc(`step${step.order}`).set(stepWithTimestamps);
            }

            // Add quiz subcollection with updated structure
            console.log(`  Adding ${quiz.length} quiz questions...`);
            for (const question of quiz) {
                const questionWithTimestamps = {
                    ...question,
                    createdAt: admin.firestore.FieldValue.serverTimestamp(),
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                };
                await moduleRef.collection('quiz').doc(`question${question.order}`).set(questionWithTimestamps);
            }

            console.log(`✅ Module ${moduleData.order} added successfully`);
        }

        // 3. Add video/image URLs to specific steps for testing
        console.log('\n🎬 Adding video/image URLs to test steps...');

        // Module 1 - Step 1: Add video
        const step1Ref = db.collection('modules').doc('module1').collection('steps').doc('step1');
        await step1Ref.update({
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('✅ Module 1 - Step 1: Added video URL');

        // Module 1 - Step 2: Add image
        const step2Ref = db.collection('modules').doc('module1').collection('steps').doc('step2');
        await step2Ref.update({
            imageUrl: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('✅ Module 1 - Step 2: Added image URL');

        // Module 2 - Step 1: Add video
        const module2Step1Ref = db.collection('modules').doc('module2').collection('steps').doc('step1');
        await module2Step1Ref.update({
            videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw',
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('✅ Module 2 - Step 1: Added video URL');

        // Module 2 - Step 2: Add image
        const module2Step2Ref = db.collection('modules').doc('module2').collection('steps').doc('step2');
        await module2Step2Ref.update({
            imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log('✅ Module 2 - Step 2: Added image URL');

        console.log('\n🎉 Mock database created successfully!');
        console.log('📝 Summary:');
        console.log(`   - ${Object.keys(courseMetadata).length} courses created`);
        console.log(`   - ${modulesData.length} modules created`);
        console.log(`   - All modules have steps and quiz questions`);
        console.log(`   - Quiz supports single/multiple choice questions`);
        console.log(`   - Test video/image URLs added to specific steps`);
        console.log('✅ Complete! Your Firestore database is now populated with mock data.');

        process.exit(0);
    } catch (error) {
        console.error('❌ Error creating mock database:', error);
        process.exit(1);
    }
}

// Run the script
createMockDatabase();
