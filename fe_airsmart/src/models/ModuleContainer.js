export default class ModuleContainer {
  constructor({ 
    id, 
    name, 
    description, 
    thumbnail, 
    modules = [], 
    totalModules = 0,
    totalSteps = 0,
    totalQuizQuestions = 0,
    createdAt, 
    updatedAt, 
    tags = [],
    isContainer = true,
    isLocked = false,
    isComingSoon = false
  }) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.thumbnail = thumbnail;
    this.modules = modules; // Array of sub-modules
    this.totalModules = totalModules;
    this.totalSteps = totalSteps;
    this.totalQuizQuestions = totalQuizQuestions;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.tags = tags;
    this.isContainer = isContainer;
    this.isLocked = isLocked;
    this.isComingSoon = isComingSoon;
  }

  // Helper methods
  getCompletedModulesCount(userProgress = {}) {
    return this.modules.filter(module => 
      userProgress[module.id]?.completed
    ).length;
  }

  getOverallProgress(userProgress = {}) {
    if (this.modules.length === 0) return 0;
    const completedCount = this.getCompletedModulesCount(userProgress);
    return (completedCount / this.modules.length) * 100;
  }

  isCompleted(userProgress = {}) {
    return this.modules.every(module => 
      userProgress[module.id]?.completed
    );
  }
}