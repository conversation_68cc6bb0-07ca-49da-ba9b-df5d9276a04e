import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Chip,
  Skeleton,
  Fade,
  LinearProgress,
  Tooltip,
  Snackbar,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import airsmartIcon from '../../../public/icon/airsmart.png';
import { fetchCoursesWithModules } from '../../../services/moduleService';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import LockIcon from '@mui/icons-material/Lock';
import { useAuth } from '../../../auth/auth.context';
import { useUserProgress } from '../../../hooks/useUserProgress';
import ModuleContainerCard from '../../../components/ModuleContainerCard';
import { getSafeImageUrl } from '../../../utils/imageHelper';
import ModuleContainer from '../../../models/ModuleContainer';

// Global cache để tránh gọi API nhiều lần khi component mount lại do StrictMode
let globalModulesCache = null;
let globalModulesLoaded = false;

const CARD_HEIGHT = 400;
const IMAGE_HEIGHT = 180;
const CONTENT_HEIGHT = CARD_HEIGHT - IMAGE_HEIGHT; // 220px for content

const StyledCard = styled(Card)(({ theme }) => ({
  height: CARD_HEIGHT,
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s, box-shadow 0.2s',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[3],
  '&:hover': {
    transform: 'translateY(-4px) scale(1.02)',
    boxShadow: theme.shadows[8],
    '& .MuiCardMedia-root': {
      filter: 'brightness(0.95)',
    },
  },
}));

const PlayButton = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  background: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  borderRadius: '50%',
  width: 48,
  height: 48,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: theme.shadows[4],
  opacity: 0.85,
  zIndex: 2,
}));

const ModuleStatus = styled(Box)(({ theme, status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return theme.palette.success.main;
      case 'in-progress':
        return theme.palette.primary.main;
      case 'locked':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[400];
    }
  };

  return {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
    backgroundColor: getStatusColor(),
    color: '#fff',
    borderRadius: '50%',
    width: 32,
    height: 32,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: theme.shadows[2],
  };
});

const ModernModuleGrid = () => {
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [imageLoaded, setImageLoaded] = useState({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const { currentUser } = useAuth();
  const {
    loading: progressLoading,
    isModuleCompleted,
    isModuleInProgress,
    getCurrentStepId,
    userProgress
  } = useUserProgress();
  const navigate = useNavigate();

  // Sử dụng ref để theo dõi xem đã tải modules chưa
  const hasLoadedModulesRef = useRef(false);
  // Loại bỏ force refresh để tránh gọi API nhiều lần
  // useUserProgress hook đã tự động load data khi cần thiết

  useEffect(() => {
    const loadModules = async () => {
      try {
        // Kiểm tra global cache trước
        if (globalModulesCache && globalModulesLoaded) {
          setModules(globalModulesCache);
          setLoading(false);
          return;
        }
        const data = await fetchCoursesWithModules(currentUser?.uid);
        // Lưu vào global cache
        globalModulesCache = data;
        globalModulesLoaded = true;

        setModules(data);
        setLoading(false);
        hasLoadedModulesRef.current = true;
      } catch (error) {
        console.error("Error fetching courses with modules:", error);
        setLoading(false);
      }
    };

    // Chỉ load nếu chưa có data
    loadModules();
  }, [currentUser?.uid]); // Reload when user changes

  const handleImageLoad = (id) => {
    setImageLoaded((prev) => ({ ...prev, [id]: true }));
  };

  const handleModuleClick = (module, index) => {
    // Check if this is a ModuleContainer
    if (module.type === 'container') {
      // ModuleContainerCard will handle its own click logic
      return;
    }

    // Get current module status
    const status = getModuleStatus(module, index);
    // Prevent navigation if module is locked
    if (status === 'locked') {
      // Show different messages based on user login status
      if (!currentUser) {
        setSnackbarMessage(`Please log in to access "${module.name}".`);
      } else {
        setSnackbarMessage(`Module "${module.name}" is locked. Complete previous modules first.`);
      }
      setSnackbarOpen(true);
      return;
    }

    // Navigate to module if not locked
    navigate(`/module/${module.id}`);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Sử dụng ref để cache trạng thái của module
  const moduleStatusCacheRef = useRef({});

  // Clear cache khi user progress data thay đổi (không phải functions)
  // Sử dụng JSON.stringify để so sánh deep equality
  const userProgressStringRef = useRef('');
  useEffect(() => {
    const currentProgressString = JSON.stringify(userProgress);
    if (currentProgressString !== userProgressStringRef.current) {
      moduleStatusCacheRef.current = {};
      moduleProgressCacheRef.current = {};
      userProgressStringRef.current = currentProgressString;
    }
  }, [userProgress]);

  const getModuleStatus = useCallback((module, index) => {
    // Sử dụng cache để tránh tính toán lại không cần thiết
    const cacheKey = `${module.id}_${index}_${!!currentUser}_${progressLoading}`;
    if (moduleStatusCacheRef.current[cacheKey]) {
      return moduleStatusCacheRef.current[cacheKey];
    }

    let status;

    // If user is not logged in, all modules except first are locked
    if (!currentUser) {
      if (index === 0) {
        status = 'not-started';
      } else {
        status = 'locked';
      }
      moduleStatusCacheRef.current[cacheKey] = status;
      return status;
    }

    // If user progress is still loading, return appropriate status
    if (progressLoading) {
      status = index === 0 ? 'not-started' : 'locked';
      moduleStatusCacheRef.current[cacheKey] = status;
      return status;
    }

    // First module is always unlocked for logged in users
    if (index === 0) {
      if (isModuleCompleted(module.id)) {
        status = 'completed';
      } else if (isModuleInProgress(module.id)) {
        status = 'in-progress';
      } else {
        status = 'not-started';
      }
    } else {
      // For other modules, check if previous module is completed
      const previousModule = modules[index - 1];

      if (!previousModule) {
        status = 'locked';
      } else {
        const isPreviousCompleted = isModuleCompleted(previousModule.id);

        if (isModuleCompleted(module.id)) {
          status = 'completed';
        } else if (isModuleInProgress(module.id)) {
          status = 'in-progress';
        } else if (isPreviousCompleted) {
          status = 'not-started';
        } else {
          status = 'locked';
        }
      }
    }

    // Cache kết quả
    moduleStatusCacheRef.current[cacheKey] = status;
    return status;
  }, [isModuleCompleted, isModuleInProgress, modules, currentUser, progressLoading]);

  const getModuleStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'in-progress':
        return <PlayArrowIcon />;
      case 'locked':
        return <LockIcon />;
      default:
        return null;
    }
  };

  // Sử dụng ref để cache tiến trình của module
  const moduleProgressCacheRef = useRef({});

  const getModuleProgress = useCallback((module) => {
    // Kiểm tra cache trước
    if (moduleProgressCacheRef.current[module.id] !== undefined) {
      return moduleProgressCacheRef.current[module.id];
    }

    const currentStepId = getCurrentStepId(module.id);
    if (!currentStepId || !module.totalSteps) {
      moduleProgressCacheRef.current[module.id] = 0;
      return 0;
    }

    // Extract step number from step ID (assuming format like "step1", "step2", etc.)
    const stepNumber = parseInt(currentStepId.replace(/\D/g, ''), 10);
    const progress = (stepNumber / module.totalSteps) * 100;

    // Lưu vào cache
    moduleProgressCacheRef.current[module.id] = progress;

    return progress;
  }, [getCurrentStepId]);

  // Show loading if modules are loading OR if user progress is loading for logged in users
  const isLoading = loading || (currentUser && progressLoading);

  if (!isLoading && modules.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>
        <SentimentDissatisfiedIcon sx={{ fontSize: 64, mb: 2 }} />
        <Typography variant="h6">No modules found</Typography>
        <Typography variant="body2">Please check back later.</Typography>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(3, 1fr)'
        },
        gap: 4,
        width: '100%'
      }}>
        {(isLoading ? Array.from(new Array(6)) : modules).map((item, idx) => {
          if (isLoading) {
            return (
              <Card key={idx} sx={{
                height: CARD_HEIGHT,
                width: '100%',
                borderRadius: 3,
                boxShadow: 3,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden'
              }}>
                <Skeleton
                  variant="rectangular"
                  height={IMAGE_HEIGHT}
                  width="100%"
                  animation="wave"
                  sx={{
                    borderRadius: '16px 16px 0 0',
                    bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)'
                  }}
                />
                <Box sx={{ p: 2, flex: 1 }}>
                  <Skeleton variant="text" height={40} width="80%" animation="wave" sx={{ mb: 1, bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }} />
                  <Skeleton variant="text" height={20} animation="wave" sx={{ mb: 0.5, bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }} />
                  <Skeleton variant="text" height={20} width="60%" animation="wave" sx={{ mb: 2, bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }} />
                  <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                    <Skeleton variant="rectangular" width={60} height={24} animation="wave" sx={{ borderRadius: 5, bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }} />
                    <Skeleton variant="rectangular" width={60} height={24} animation="wave" sx={{ borderRadius: 5, bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }} />
                  </Box>
                </Box>
              </Card>
            );
          }

          // Check if this is a ModuleContainer
          if (item.isContainer) {
            // Thêm logic khóa course nếu course trước chưa hoàn thành
            let isLocked = false;
            if (idx > 0) {
              // Tìm course trước đó (chỉ tính các container)
              let prevIdx = idx - 1;
              while (prevIdx >= 0 && !modules[prevIdx].isContainer) prevIdx--;
              if (prevIdx >= 0) {
                const prevContainer = modules[prevIdx];
                // Sử dụng isCompleted helper
                isLocked = !prevContainer.isCompleted(userProgress);
              }
            }
            // Tạo lại instance ModuleContainer để giữ prototype
            const containerInstance = new ModuleContainer({ ...item, isLocked });
            return (
              <ModuleContainerCard
                key={item.id}
                container={containerInstance}
              />
            );
          }

          // Original module card rendering for individual modules
          const module = item;
          const imgSrc = getSafeImageUrl(module.thumbnail) || airsmartIcon;

          return (
            <StyledCard
              key={module.id}
              onClick={() => handleModuleClick(module, idx)}
              sx={{
                opacity: getModuleStatus(module, idx) === 'locked' ? 0.7 : 1,
                cursor: getModuleStatus(module, idx) === 'locked' ? 'not-allowed' : 'pointer',
                '&:hover': {
                  transform: getModuleStatus(module, idx) === 'locked' ? 'none' : 'translateY(-4px) scale(1.02)',
                }
              }}
            >
              <Box sx={{ position: 'relative', height: IMAGE_HEIGHT, width: '100%', overflow: 'hidden' }}>
                {!imageLoaded[module.id] && (
                  <Skeleton
                    variant="rectangular"
                    height={IMAGE_HEIGHT}
                    width="100%"
                    animation="wave"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      zIndex: 1,
                      borderRadius: '16px 16px 0 0',
                      bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)'
                    }}
                  />
                )}
                <Fade in={!!imageLoaded[module.id]} timeout={600}>
                  <CardMedia
                    component="img"
                    height={IMAGE_HEIGHT}
                    image={imgSrc}
                    alt={module.name}
                    sx={{
                      objectFit: 'cover',
                      width: '100%',
                      display: imageLoaded[module.id] ? 'block' : 'none',
                      borderRadius: '16px 16px 0 0',
                      height: IMAGE_HEIGHT,
                      minHeight: IMAGE_HEIGHT,
                      maxHeight: IMAGE_HEIGHT,
                      filter: getModuleStatus(module, idx) === 'locked' ? 'grayscale(0.8)' : 'none',
                    }}
                    onLoad={() => handleImageLoad(module.id)}
                  />
                </Fade>

                {/* Module status indicator */}
                {currentUser && !progressLoading && (
                  <Tooltip title={getModuleStatus(module, idx).replace('-', ' ').toUpperCase()}>
                    <ModuleStatus status={getModuleStatus(module, idx)}>
                      {getModuleStatusIcon(getModuleStatus(module, idx))}
                    </ModuleStatus>
                  </Tooltip>
                )}

                {getModuleStatus(module, idx) !== 'locked' && (
                  <PlayButton>
                    <PlayArrowIcon fontSize="large" />
                  </PlayButton>
                )}
              </Box>
              <CardContent sx={{
                height: CONTENT_HEIGHT,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                overflow: 'hidden',
                p: 2,
              }}>
                {/* Top section - Title and Description */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0,
                  flex: '0 0 auto'
                }}>
                  <Typography
                    gutterBottom
                    variant="h6"
                    component="div"
                    noWrap
                    sx={{
                      fontWeight: 700,
                      fontSize: '1.1rem',
                      lineHeight: 1.2,
                      mb: 0.5,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      height: '1.3rem', // Fixed height
                    }}
                  >
                    {module.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 1,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      height: '40px', // Fixed height for 2 lines
                      lineHeight: '20px',
                    }}
                  >
                    {module.description}
                  </Typography>
                </Box>

                {/* Middle section - Progress and Module Info */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  flex: '1 1 auto',
                  justifyContent: 'center',
                  minHeight: '60px', // Reserve space for progress or module info
                }}>
                  {/* Progress indicator for logged in users */}
                  {currentUser && isModuleInProgress(module.id) ? (
                    <Box sx={{ width: '100%', mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          Progress
                        </Typography>
                        <Typography variant="caption" color="primary">
                          {Math.round(getModuleProgress(module))}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={getModuleProgress(module)}
                        sx={{ height: 4, borderRadius: 2 }}
                      />
                    </Box>
                  ) : (
                    <Box sx={{ height: '32px' }} /> // Placeholder to maintain consistent spacing
                  )}

                  {/* Module info */}
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    height: '20px', // Fixed height
                    alignItems: 'center'
                  }}>
                    <Typography variant="caption" color="text.secondary">
                      {module.totalSteps} steps
                    </Typography>
                    {module.totalQuizQuestions > 0 && (
                      <Typography variant="caption" color="text.secondary">
                        {module.totalQuizQuestions} quiz questions
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* Bottom section - Tags */}
                <Box sx={{
                  display: 'flex',
                  gap: 1,
                  flexWrap: 'nowrap',
                  height: '28px', // Fixed height
                  overflow: 'hidden',
                  width: '100%',
                  alignItems: 'center',
                  flex: '0 0 auto'
                }}>
                  {module.tags?.slice(0, 3).map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      size="small"
                      sx={{
                        backgroundColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.1)'
                            : 'rgba(0, 0, 0, 0.08)',
                        fontSize: 12,
                        height: 24,
                        maxWidth: 70,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    />
                  ))}
                </Box>
              </CardContent>
            </StyledCard>
          );
        })}
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="warning" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ModernModuleGrid;