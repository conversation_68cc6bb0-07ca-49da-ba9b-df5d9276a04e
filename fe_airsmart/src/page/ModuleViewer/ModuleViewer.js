import React, { Suspense, useEffect, useState, useCallback, useRef, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Canvas, useThree, useFrame } from "@react-three/fiber";
import { OrbitControls, useProgress, Html } from "@react-three/drei";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { convertToSignedUrl } from "../../utils/wasabiHelper";
import * as THREE from 'three';

import introJs from 'intro.js';
import 'intro.js/introjs.css';
import {
  Button,
  Typography,
  Container,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Box,
  CircularProgress,
  Alert,
  Snackbar,
  IconButton
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import <PERSON>ForwardIcon from '@mui/icons-material/ArrowForward';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CameraIcon from '@mui/icons-material/CameraAlt';

import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

import MainLayout from "../../components/MainLayout";
import ContentGuide from "../../components/ContentGuide";
import { useAuth } from "../../auth/auth.context";
import { useUserProgress } from "../../hooks/useUserProgress";
import { getModule, getModuleSteps, getNextUncompletedModule, getModuleQuiz } from "../../services/moduleService";
import { updateModuleProgress, getModuleProgress } from "../../services/userProgressService";
import Hotspot from "./components/Hotspot";

// Loader component to show loading progress
function Loader() {
  const { progress } = useProgress();
  return <Html center>{progress.toFixed(0)}% loaded</Html>;
}

// 3D Model Component - GLTF/GLB Only (similar to StepEditor)
function Model({ modelUrl, onModelClick }) {
  const meshRef = useRef();
  const [loadedModel, setLoadedModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (modelUrl) {
      setLoading(true);
      setError(null);
      setLoadedModel(null);
      
      const gltfLoader = new GLTFLoader();

      // Setup DRACO loader for compressed models
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      gltfLoader.setDRACOLoader(dracoLoader);
      
      gltfLoader.load(
        modelUrl,
        // onLoad
        (gltf) => {
          // Scale model to fit in view
          const box = new THREE.Box3().setFromObject(gltf.scene);
          const size = box.getSize(new THREE.Vector3()).length();
          const scale = size > 0 ? 2 / size : 1;
          gltf.scene.scale.setScalar(scale);

          // Center the model
          const center = box.getCenter(new THREE.Vector3());
          gltf.scene.position.copy(center).multiplyScalar(-scale);

          // Enable shadows on all meshes
          gltf.scene.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });

          setLoadedModel(gltf);
          setLoading(false);
        },
        // onProgress
        (progress) => {
          if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
          }
        },
        // onError
        (error) => {
          console.error('Error loading GLTF model:', error);
          setError(error);
          setLoading(false);
        }
      );
    } else {
      setLoadedModel(null);
      setLoading(false);
      setError(null);
    }
  }, [modelUrl]);
  
  useFrame(() => {
    if (meshRef.current) {
      // Any animations can go here
    }
  });

  // Show loading state
  if (loading) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          Loading GLTF Model...
        </div>
      </Html>
    );
  }

  // Show error state
  if (error) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(255,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center',
          maxWidth: '200px'
        }}>
          <div>❌ Failed to load GLTF model</div>
          <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
            Check URL and CORS settings
          </div>
        </div>
      </Html>
    );
  }

  // Only render if we have a valid model
  if (!loadedModel) {
    return null;
  }

  return (
    <primitive 
      ref={meshRef}
      object={loadedModel.scene} 
      scale={[1, 1, 1]}
      onClick={onModelClick}
    />
  );
}

// Fallback component for when no model is loaded
function DefaultScene() {
  return (
    <mesh>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial color="#cccccc" />
    </mesh>
  );
}

// Updated ModelWithMemo component to use GLTF/GLB only
const ModelWithMemo = React.memo(function ModelWithMemo({ modelUrl }) {
  // Check if URL is from Wasabi storage
  const isWasabiUrl = (url) => {
    if (!url) return false;
    return url.includes('wasabisys.com') ||
           url.includes('airsmart') ||
           !url.includes('://'); // If no protocol, assume it's a Wasabi path
  };

  // Process URL - create signed URL if it's a Wasabi URL
  let finalUrl = modelUrl;
  if (isWasabiUrl(finalUrl)) {
    finalUrl = convertToSignedUrl(finalUrl);
  }
  return (
    <Suspense fallback={null}>
      {finalUrl ? (
        <Model modelUrl={finalUrl} />
      ) : (
        <DefaultScene />
      )}
    </Suspense>
  );
});

// Camera controller component to handle step-specific camera settings
const CameraController = ({ cameraSettings, controlsRef }) => {
  const { camera } = useThree();

  // Effect to sync camera position when cameraSettings prop changes - matching admin_panel approach
  useEffect(() => {
    // Add a small delay to ensure OrbitControls is fully initialized
    const timer = setTimeout(() => {
      if (controlsRef.current && cameraSettings) {
        // Set camera position
        if (cameraSettings.position) {
          camera.position.set(
            cameraSettings.position[0],
            cameraSettings.position[1],
            cameraSettings.position[2]
          );
        }

        // Set camera target (look at)
        if (cameraSettings.target) {
          controlsRef.current.target.set(
            cameraSettings.target[0],
            cameraSettings.target[1],
            cameraSettings.target[2]
          );
        }

        // Update the controls to apply changes
        controlsRef.current.update();
      }
    }, 100); // Small delay to ensure OrbitControls is ready

    return () => clearTimeout(timer);
  }, [camera, cameraSettings, controlsRef]);

  return null;
};

// Reset camera function
const resetCameraToDefault = (camera, controlsRef, currentStep) => {
  if (!controlsRef.current) {
    console.warn('Controls not available');
    return false;
  }

  // Get default position and target from current step or use fallback
  const defaultPosition = currentStep?.camera?.position || [0, 0, 5];
  const defaultTarget = currentStep?.camera?.target || [0, 0, 0];
  // Set camera position
  camera.position.set(
    defaultPosition[0],
    defaultPosition[1],
    defaultPosition[2]
  );
  
  // Set camera target
  controlsRef.current.target.set(
    defaultTarget[0],
    defaultTarget[1],
    defaultTarget[2]
  );
  
  // Update controls to apply changes
  controlsRef.current.update();
  return true;
};

// Hotspots component - using new Hotspot component with tooltip
const HotspotsRenderer = React.memo(function HotspotsRenderer({ hotspots = [], onHotspotClick }) {
  return (
    <>
      {hotspots && hotspots.length > 0 && (
        hotspots.map((hotspot, index) => (
          <Hotspot
            key={hotspot.id || index}
            hotspot={hotspot}
            index={index}
            onHotspotClick={onHotspotClick}
          />
        ))
      )}
    </>
  );
});

// Enhanced Image Viewer Component with Annotations
const ImageViewerWithAnnotations = React.memo(function ImageViewerWithAnnotations({ step }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleError = () => {
    setLoading(false);
    setError('Failed to load image');
  };

  // Check if URL is from Wasabi storage
  const isWasabiUrl = (url) => {
    if (!url) return false;
    return url.includes('wasabisys.com') ||
           url.includes('airsmart') ||
           !url.includes('://');
  };

  // Process image URL - convert to signed URL if from Wasabi
  const processImageUrl = (imageUrl) => {
    if (!imageUrl) return null;
    
    if (isWasabiUrl(imageUrl)) {
      const signedUrl = convertToSignedUrl(imageUrl);
      return signedUrl;
    }
    
    return imageUrl;
  };

  const finalImageUrl = processImageUrl(step?.imageUrl);
  const imageAnnotations = step?.imageAnnotations || [];

  return (
    <Box sx={{ 
      width: '100%', 
      height: '100%', 
      position: 'relative', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      overflow: 'hidden'
    }}>
      {loading && (
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading image...
          </Typography>
        </Box>
      )}

      {error && (
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      )}

      {/* Image with Annotations */}
      <Box sx={{ position: 'relative', width: '100%', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <img
          src={finalImageUrl}
          alt={step.title}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            objectFit: 'contain',
            borderRadius: '8px',
            display: loading ? 'none' : 'block'
          }}
          onLoad={handleLoad}
          onError={handleError}
        />
        
        {/* Image Annotations Overlay */}
        {!loading && imageAnnotations.length > 0 && imageAnnotations.map((annotation, index) => (
          <Box
            key={annotation.id || index}
            sx={{
              position: 'absolute',
              left: `${annotation.position?.x || 50}%`,
              top: `${annotation.position?.y || 50}%`,
              transform: 'translate(-50%, -50%)',
              zIndex: 10,
              pointerEvents: 'auto'
            }}
          >
            <Button
              variant="contained"
              size="small"
              color={annotation.style?.color || 'primary'}
              onClick={() => setSelectedAnnotation(annotation)}
              sx={{
                minWidth: 'auto',
                borderRadius: '50%',
                width: 32,
                height: 32,
                fontSize: '12px',
                fontWeight: 'bold',
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                '&:hover': {
                  transform: 'scale(1.1)',
                  transition: 'transform 0.2s'
                }
              }}
            >
              {index + 1}
            </Button>
          </Box>
        ))}
      </Box>

      {/* Annotation Details Modal/Overlay */}
      {selectedAnnotation && (
        <Box
          sx={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 2,
            p: 2,
            maxWidth: '300px',
            zIndex: 20,
            boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
            border: '1px solid #e0e0e0'
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>
              {selectedAnnotation.title}
            </Typography>
            <IconButton 
              size="small" 
              onClick={() => setSelectedAnnotation(null)}
              sx={{ p: 0.5, ml: 1 }}
            >
              ✕
            </IconButton>
          </Box>
          {selectedAnnotation.description && (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {selectedAnnotation.description}
            </Typography>
          )}
        </Box>
      )}
      
      {/* Annotations Info Badge */}
      {/* {imageAnnotations.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: '20px',
            left: '20px',
            background: 'rgba(25, 118, 210, 0.9)',
            color: 'white',
            borderRadius: 1,
            px: 1.5,
            py: 0.5,
            fontSize: '12px',
            fontWeight: 'bold'
          }}
        >
          📍 {imageAnnotations.length} annotation{imageAnnotations.length !== 1 ? 's' : ''}
        </Box>
      )} */}
    </Box>
  );
});

// Enhanced Video Viewer Component with Annotations  
const VideoViewerWithAnnotations = React.memo(function VideoViewerWithAnnotations({ step }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);
  const videoRef = useRef(null);

  // Memoize URLs to prevent recreation on every render
  const finalVideoUrl = useMemo(() => {
    const processVideoUrl = (videoUrl) => {
      if (!videoUrl) return null;
      
      const isWasabiUrl = (url) => {
        if (!url) return false;
        return url.includes('wasabisys.com') ||
               url.includes('airsmart') ||
               !url.includes('://');
      };
      
      if (isWasabiUrl(videoUrl)) {
        const signedUrl = convertToSignedUrl(videoUrl);
        return signedUrl;
      }
      
      return videoUrl;
    };
    
    return processVideoUrl(step?.videoUrl);
  }, [step?.videoUrl]);

  // Memoize video type detection
  const { isEmbed, embedUrl } = useMemo(() => {
    const isEmbedUrl = (url) => {
      if (!url) return false;
      return url.includes('youtube.com/embed') || 
             url.includes('youtu.be') || 
             url.includes('vimeo.com') ||
             url.includes('player.vimeo.com');
    };

    const getEmbedUrl = (url) => {
      if (!url) return null;
      
      // YouTube URLs
      if (url.includes('youtube.com/watch?v=')) {
        const videoId = url.split('v=')[1]?.split('&')[0];
        return `https://www.youtube.com/embed/${videoId}`;
      }
      if (url.includes('youtu.be/')) {
        const videoId = url.split('youtu.be/')[1]?.split('?')[0];
        return `https://www.youtube.com/embed/${videoId}`;
      }
      
      // Vimeo URLs
      if (url.includes('vimeo.com/') && !url.includes('player.vimeo.com')) {
        const videoId = url.split('vimeo.com/')[1]?.split('?')[0];
        return `https://player.vimeo.com/video/${videoId}`;
      }
      if (url.includes('player.vimeo.com/video/')) {
        const videoId = url.split('video/')[1]?.split('?')[0];
        return `https://player.vimeo.com/video/${videoId}`;
      }
      
      return url;
    };

    const isEmbedResult = isEmbedUrl(finalVideoUrl);
    const embedUrlResult = isEmbedResult ? getEmbedUrl(finalVideoUrl) : null;
    
    return {
      isEmbed: isEmbedResult,
      embedUrl: embedUrlResult
    };
  }, [finalVideoUrl]);

  // Memoize video annotations to prevent array recreation
  const videoAnnotations = useMemo(() => step?.videoAnnotations || [], [step?.videoAnnotations]);

  // Memoize event handlers to prevent recreation
  const handleLoad = useCallback(() => {
    setLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setLoading(false);
    setError('Failed to load video');
  }, []);

  // Handle time update for video annotations
  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  }, []);

  // Only setup event listeners when video ref changes and it's not embed
  useEffect(() => {
    const video = videoRef.current;
    if (video && !isEmbed) {
      video.addEventListener('timeupdate', handleTimeUpdate);
      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate);
      };
    }
  }, [isEmbed, handleTimeUpdate]);

  // Reset loading state when video URL changes
  useEffect(() => {
    if (finalVideoUrl) {
      setLoading(true);
      setError(null);
    }
  }, [finalVideoUrl]);

  return (
    <Box sx={{ 
      width: '100%', 
      height: '100%', 
      position: 'relative', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center' 
    }}>
      {loading && (
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading video...
          </Typography>
        </Box>
      )}

      {error && (
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      )}

      {/* Video Player */}
      <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
        {isEmbed ? (
          // Render iframe for embed URLs (YouTube, Vimeo, etc.)
          <iframe
            key={embedUrl} // Add key to force remount only when URL actually changes
            src={embedUrl}
            width="100%"
            height="100%"
            style={{ 
              border: 'none', 
              borderRadius: '8px',
              minHeight: '400px',
              display: loading ? 'none' : 'block'
            }}
            onLoad={handleLoad}
            onError={handleError}
            title={`Video for ${step?.title || 'Step'}`}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        ) : (
          // Render video element for direct video files
          <video
            key={finalVideoUrl} // Add key to force remount only when URL actually changes
            ref={videoRef}
            src={finalVideoUrl}
            controls
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              borderRadius: '8px',
              display: loading ? 'none' : 'block'
            }}
            onLoadedData={handleLoad}
            onError={handleError}
          />
        )}
        
        {/* Video Annotations Overlay - Only show for direct video files */}
        {!isEmbed && !loading && videoAnnotations.length > 0 && videoAnnotations.map((annotation, index) => {
          const isActive = currentTime >= (annotation.startTime || 0) && currentTime <= (annotation.endTime || Infinity);
          if (!isActive) return null;

          return (
            <Box
              key={annotation.id || index}
              sx={{
                position: 'absolute',
                left: `${annotation.position?.x || 50}%`,
                top: `${annotation.position?.y || 50}%`,
                transform: 'translate(-50%, -50%)',
                zIndex: 10,
                pointerEvents: 'auto'
              }}
            >
              <Button
                variant="contained"
                size="small"
                color={annotation.style?.color || 'primary'}
                onClick={() => setSelectedAnnotation(annotation)}
                sx={{
                  minWidth: 'auto',
                  borderRadius: '50%',
                  width: 32,
                  height: 32,
                  fontSize: '12px',
                  fontWeight: 'bold',
                  animation: 'pulse 2s infinite',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  '@keyframes pulse': {
                    '0%': {
                      transform: 'scale(1)',
                    },
                    '50%': {
                      transform: 'scale(1.1)',
                    },
                    '100%': {
                      transform: 'scale(1)',
                    },
                  }
                }}
              >
                {index + 1}
              </Button>
            </Box>
          );
        })}
      </Box>

      {/* Annotation Details Modal/Overlay */}
      {selectedAnnotation && (
        <Box
          sx={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 2,
            p: 2,
            maxWidth: '300px',
            zIndex: 20,
            boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
            border: '1px solid #e0e0e0'
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>
              {selectedAnnotation.title}
            </Typography>
            <IconButton 
              size="small" 
              onClick={() => setSelectedAnnotation(null)}
              sx={{ p: 0.5, ml: 1 }}
            >
              ✕
            </IconButton>
          </Box>
          {selectedAnnotation.description && (
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
              {selectedAnnotation.description}
            </Typography>
          )}
          {selectedAnnotation.startTime !== undefined && (
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              ⏱️ {Math.floor(selectedAnnotation.startTime)}s - {Math.floor(selectedAnnotation.endTime)}s
            </Typography>
          )}
        </Box>
      )}

      {/* Video Annotations Info Badge */}
      {/* {videoAnnotations.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: '20px',
            left: '20px',
            background: 'rgba(25, 118, 210, 0.9)',
            color: 'white',
            borderRadius: 1,
            px: 1.5,
            py: 0.5,
            fontSize: '12px',
            fontWeight: 'bold'
          }}
        >
          🎬 {videoAnnotations.length} annotation{videoAnnotations.length !== 1 ? 's' : ''}
        </Box>
      )} */}

      {/* Embed Video Info Badge */}
      {isEmbed && (
        <Box
          sx={{
            position: 'absolute',
            top: '20px',
            left: '20px',
            background: 'rgba(0,0,0,0.7)',
            color: 'white',
            borderRadius: 1,
            px: 1.5,
            py: 0.5,
            fontSize: '12px',
            fontWeight: 'bold'
          }}
        >
          🔗 External Video
        </Box>
      )}
    </Box>
  );
});

// Enhanced Media Viewer Component that chooses the right viewer
const MediaViewer = React.memo(function MediaViewer({ step }) {
  const hasVideo = step?.videoUrl;
  const hasImage = step?.imageUrl;

  if (!hasVideo && !hasImage) {
    return null;
  }

  if (hasVideo) {
    return <VideoViewerWithAnnotations step={step} />;
  }

  if (hasImage) {
    return <ImageViewerWithAnnotations step={step} />;
  }

  return null;
});

function ModuleViewer() {
  const { moduleId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { isModuleCompleted: checkModuleCompleted } = useUserProgress();

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [module, setModule] = useState(null);
  const [steps, setSteps] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [moduleCompleted, setModuleCompleted] = useState(false);
  const [nextModule, setNextModule] = useState(null);
  const [hasQuiz, setHasQuiz] = useState(false); // New state to track if module has quiz
  const [quizLoading, setQuizLoading] = useState(false); // Loading state for quiz check
  const [initialLoadComplete, setInitialLoadComplete] = useState(false); // Track initial load
  const [showContentGuide, setShowContentGuide] = useState(false); // Content guide state

  // Refs to track progress updates
  const prevStepRef = useRef(null);
  // Ref for OrbitControls - matching EditStepEditor approach
  const controlsRef = useRef();
  const isUpdatingProgressRef = useRef(false); // Prevent multiple simultaneous updates

  // Handler for navigating to next module
  const handleNextModule = () => {
    if (nextModule) {
      navigate(`/module/${nextModule.id}`);
    } else {
      navigate('/');
    }
  };

  // Handler for hotspot click
  const handleHotspotClick = useCallback((hotspot) => {
    // You can add additional logic here if needed
    // For example, tracking analytics, showing additional info, etc.
  }, []);

  // Effect to load next module when current module is completed
  useEffect(() => {
    const loadNextModule = async () => {
      if (moduleCompleted && currentUser) {
        try {
          const nextModuleData = await getNextUncompletedModule(moduleId, checkModuleCompleted);
          setNextModule(nextModuleData);
        } catch (error) {
          console.error('Error loading next module:', error);
        }
      }
    };

    loadNextModule();
  }, [moduleCompleted, moduleId, currentUser, checkModuleCompleted]);

  // Load module and steps data
  useEffect(() => {
    let isMounted = true;

    const loadModuleData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get module data
        const moduleData = await getModule(moduleId);
        if (!isMounted) return;

        if (!moduleData) {
          setError('Module not found');
          setLoading(false);
          return;
        }
        setModule(moduleData);

        // Get steps data
        const stepsData = await getModuleSteps(moduleId);
        if (!isMounted) return;

        if (stepsData.length === 0) {
          setError('No steps found for this module');
          setLoading(false);
          return;
        }

        // Steps are already sorted by order from backend
        setSteps(stepsData);

        // Set default active step
        setActiveStep(0);
        setModuleCompleted(false);

        // Show content guide for "introduction" module
        if (moduleData.name === 'Introduction' && currentUser) {
          // Small delay to ensure UI is rendered
          setTimeout(() => {
            setShowContentGuide(true);
          }, 1000);
        }

        // Check if module has quiz questions
        try {
          setQuizLoading(true);
          const quizData = await getModuleQuiz(moduleId);
          if (isMounted) {
            if (quizData && quizData.length > 0) {
              setHasQuiz(true);
            } else {
              setHasQuiz(false);
            }
          }
        } catch (quizError) {
          console.warn('Error checking quiz data:', quizError);
          if (isMounted) {
            setHasQuiz(false); // Default to no quiz if error
          }
        } finally {
          if (isMounted) {
            setQuizLoading(false);
          }
        }

        if (isMounted) {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error loading module data:', err);
        if (isMounted) {
          setError('Failed to load module data');
          setLoading(false);
        }
      }
    };

    loadModuleData();

    return () => {
      isMounted = false;
    };
  }, [moduleId]); // Chỉ phụ thuộc vào moduleId để tránh gọi API nhiều lần

  // Load user progress separately and set initial activeStep correctly
  useEffect(() => {
    const loadUserProgress = async () => {
      if (!currentUser || !steps.length || initialLoadComplete) {
        return;
      }

      try {
        // Lấy tiến trình hiện tại của người dùng cho module này
        const moduleProgress = await getModuleProgress(currentUser.uid, moduleId);
        
        if (moduleProgress && (moduleProgress.stepId || moduleProgress.currentStepId)) {
          // Sử dụng currentStepId hoặc stepId tùy thuộc vào cấu trúc dữ liệu
          const savedStepId = moduleProgress.currentStepId || moduleProgress.stepId;
          
          // Kiểm tra xem module đã hoàn thành chưa
          if (savedStepId === 'completed') {
            // Nếu module đã hoàn thành, đặt activeStep là bước cuối cùng
            setActiveStep(steps.length - 1);
            setModuleCompleted(true);
          } else {
            // Tìm index của step trong danh sách steps
            const stepIndex = steps.findIndex(step => step.id === savedStepId);
            if (stepIndex !== -1) {
              setActiveStep(stepIndex);
            } else {
              // Nếu không tìm thấy step, bắt đầu từ step đầu tiên
              setActiveStep(0);
            }
            setModuleCompleted(false);
          }
        } else {
          // Nếu chưa có tiến trình, bắt đầu từ step đầu tiên
          setActiveStep(0);
          setModuleCompleted(false);
        }
        
        // Đánh dấu đã hoàn thành việc load initial data
        setInitialLoadComplete(true);
      } catch (err) {
        console.error('Error getting module progress:', err);
        setActiveStep(0);
        setModuleCompleted(false);
        setInitialLoadComplete(true);
      }
    };

    loadUserProgress();
  }, [currentUser, moduleId, steps, initialLoadComplete]);

  // Cache để tránh gọi API nhiều lần cho cùng một step
  const progressCacheRef = useRef({});

  // Update user progress when active step changes - FIXED LOGIC
  const updateProgress = useCallback(async () => {
    // Chỉ update progress sau khi đã load xong initial data
    if (!currentUser || !module || steps.length === 0 || activeStep >= steps.length || !initialLoadComplete) {
      return;
    }

    // Prevent concurrent updates
    if (isUpdatingProgressRef.current) {
      return;
    }

    const currentStep = steps[activeStep];
    const progressKey = `${moduleId}_${currentStep.id}`;

    // Kiểm tra cache trước khi gọi API
    if (progressCacheRef.current[progressKey]) {
      return;
    }

    // Only update when step changes
    if (prevStepRef.current !== progressKey) {
      prevStepRef.current = progressKey;
      isUpdatingProgressRef.current = true;

      try {
        // Kiểm tra xem module đã hoàn thành chưa
        const moduleProgress = await getModuleProgress(currentUser.uid, moduleId);
        const savedStepId = moduleProgress?.currentStepId || moduleProgress?.stepId;

        // Nếu module đã hoàn thành, không cập nhật lại
        if (savedStepId === 'completed') {
          setModuleCompleted(true);
          progressCacheRef.current[progressKey] = true;
          setSnackbarMessage('Module already completed');
          setSnackbarOpen(true);
          return;
        }

        // Chỉ update nếu step hiện tại khác với step đã lưu hoặc chưa có progress
        if (!savedStepId || savedStepId !== currentStep.id) {
          await updateModuleProgress(
            currentUser.uid,
            moduleId,
            currentStep.id,
            false, // not completed yet
            null // no quiz score yet
          );

          // Cache kết quả thành công
          progressCacheRef.current[progressKey] = true;
          
          setSnackbarMessage(`Progress updated to ${currentStep.title}`);
          setSnackbarOpen(true);
        } else {
          // Nếu step đã được lưu rồi, chỉ cache kết quả
          progressCacheRef.current[progressKey] = true;
        }
      } catch (err) {
        console.error('Error updating progress:', err);
        setSnackbarMessage('Error updating progress');
        setSnackbarOpen(true);
      } finally {
        isUpdatingProgressRef.current = false;
      }
    }
  }, [activeStep, currentUser, moduleId, module, steps, initialLoadComplete]);

  // Call updateProgress only when activeStep changes
  useEffect(() => {
    // Chỉ cập nhật khi có đủ dữ liệu
    if (!currentUser || !module || !steps.length) {
      return;
    }

    // Lấy thông tin về bước hiện tại
    const currentStep = steps[activeStep];
    if (!currentStep) {
      return;
    }

    // Tạo key duy nhất cho mỗi bước
    const stepKey = `${moduleId}_${currentStep.id}`;

    // Kiểm tra cache trước khi gọi API
    if (progressCacheRef.current[stepKey]) {
      return;
    }
    // Debounce để tránh gọi API quá nhiều lần
    const timer = setTimeout(() => {
      updateProgress();
    }, 300); // Giảm thời gian debounce

    return () => {
      clearTimeout(timer);
    };
  }, [activeStep, currentUser, moduleId, module, steps, updateProgress]);

  // Handler for completing module when no quiz
  const handleCompleteModule = async () => {
    try {
      // Mark module as completed
      await updateModuleProgress(
        currentUser.uid,
        moduleId,
        'completed', // Special stepId to indicate completion
        true, // Module is completed
        null // No quiz score
      );
      
      setModuleCompleted(true);
      setSnackbarMessage('Module completed successfully!');
      setSnackbarOpen(true);
      
      // Load next module
      try {
        const nextModuleData = await getNextUncompletedModule(moduleId, checkModuleCompleted);
        setNextModule(nextModuleData);
      } catch (error) {
        console.error('Error loading next module:', error);
      }
    } catch (error) {
      console.error('Error completing module:', error);
      setSnackbarMessage('Error completing module');
      setSnackbarOpen(true);
    }
  };

  // Navigation handlers - FIXED LOGIC
  const handleNext = () => {
    // Đơn giản hóa logic: chỉ chuyển đến step tiếp theo
    // Việc update progress sẽ được xử lý bởi useEffect khác
    setActiveStep((prevActiveStep) => {
      const nextStep = prevActiveStep + 1;
      if (nextStep < steps.length) {
        // Clear cache cho step tiếp theo để đảm bảo nó được cập nhật
        const nextStepKey = `${moduleId}_${steps[nextStep].id}`;
        delete progressCacheRef.current[nextStepKey];
        return nextStep;
      }
      return prevActiveStep; // Không thay đổi nếu đã ở step cuối
    });
  };

  const handleBack = () => {
    // Đơn giản hóa logic: chỉ chuyển đến step trước đó
    setActiveStep((prevActiveStep) => {
      const previousStep = prevActiveStep - 1;
      if (previousStep >= 0) {
        // Clear cache cho step trước đó để đảm bảo nó được cập nhật lại
        const previousStepKey = `${moduleId}_${steps[previousStep].id}`;
        delete progressCacheRef.current[previousStepKey];
        return previousStep;
      }
      return prevActiveStep; // Không thay đổi nếu đã ở step đầu
    });
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Start interactive tour
  const startIntro = () => {
    const currentStep = steps[activeStep];
  
    if (!currentStep) {
      setSnackbarMessage('No tour available for this step');
      setSnackbarOpen(true);
      return;
    }
    // Wait for elements to be rendered
    setTimeout(() => {
      let introSteps = [];
  
      // Only use hotspots for tour - introjsSteps functionality disabled
      if (currentStep.hotspots && currentStep.hotspots.length > 0) {
        // Add module viewer container as first step
        const viewerContainer = document.querySelector('#module-viewer-container');
        if (viewerContainer) {
          introSteps.push({
            element: viewerContainer,
            intro: 'This is the 3D model viewer area. You can rotate, zoom, and pan the model.',
            position: 'bottom'
          });
        }
  
        // Add hotspots to tour
        currentStep.hotspots.forEach((hotspot) => {
          const element = document.querySelector(`#${hotspot.id}`);
          if (element) {
            introSteps.push({
              element,
              intro: hotspot.description || hotspot.intro || `This is ${hotspot.label || hotspot.id}`,
              position: hotspot.introPosition || 'bottom'
            });
          } else {
            console.warn(`Hotspot element not found: #${hotspot.id}`);
          }
        });
      }
  
      // Always add tour button as last step
      const tourButton = document.querySelector('#tour-button');
      if (tourButton) {
        introSteps.push({
          element: tourButton,
          intro: 'Click here to restart the tour anytime.',
          position: 'left'
        });
      }
  
      if (introSteps.length === 0) {
        setSnackbarMessage('No tour available for this step');
        setSnackbarOpen(true);
        return;
      }
      // Start IntroJS with improved options
      introJs().setOptions({
        steps: introSteps,
        nextLabel: 'Next',
        prevLabel: 'Back',
        skipLabel: 'Skip',
        doneLabel: 'Done',
        // Improved positioning options
        scrollToElement: true,
        showStepNumbers: false,
        exitOnOverlayClick: false,
        exitOnEsc: true,
        helperElementPadding: 10,
        positionPrecedence: ["bottom", "top", "right", "left"],
        // Add callbacks for debugging
        onbeforechange: function(targetElement) {
          if (targetElement) {
            // Ensure element is visible
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        },
        onchange: function(targetElement) {
        }
      }).start();
    }, 1000); // Increased timeout to ensure 3D elements are rendered
  };

  // Loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        
        <Alert severity="error">{error}</Alert>
        <Button variant="contained" onClick={() => navigate('/')} sx={{ mt: 2 }}>
          Back to Home
        </Button>
      </Box>
    );
  }

  // No content state
  if (!module || steps.length === 0) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">No content available</Alert>
        <Button variant="contained" onClick={() => navigate('/')} sx={{ mt: 2 }}>
          Back to Home
        </Button>
      </Box>
    );
  }

  const currentStep = steps[activeStep];

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Module Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>{module.name}</Typography>
            <Typography variant="subtitle1" color="text.secondary">{module.description}</Typography>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* Media/3D Viewer */}
          <Paper
            sx={{
              p: 0,
              position: 'relative',
              height: '70vh',
              width: { xs: '100%', md: '60%' },
              mb: { xs: 3, md: 0 }
            }}
            id="module-viewer-container"
          >
            <IconButton
              id="tour-button"
              color="primary"
              onClick={startIntro}
              sx={{ position: 'absolute', top: 16, left: 16, zIndex: 1000, bgcolor: 'rgba(255,255,255,0.7)' }}
              aria-label="Start Tour"
            >
              <HelpOutlineIcon />
            </IconButton>

            {/* Reset Camera Button - NEW */}
            <IconButton
              color="primary"
              onClick={() => {
                const { camera } = controlsRef.current ? { camera: controlsRef.current.object } : {};
                if (camera && controlsRef.current) {
                  const success = resetCameraToDefault(camera, controlsRef, currentStep);
                  if (success) {
                    setSnackbarMessage('Camera reset to default position');
                    setSnackbarOpen(true);
                  }
                } else {
                  setSnackbarMessage('Camera controls not available');
                  setSnackbarOpen(true);
                }
              }}
              sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1000, bgcolor: 'rgba(255,255,255,0.7)' }}
              aria-label="Reset Camera"
            >
              <CameraIcon />
            </IconButton>

            {/* Conditional rendering: Video/Image OR 3D Model */}
            {(currentStep?.videoUrl || currentStep?.imageUrl) ? (
              // Display video or image if available
              <Box sx={{ width: '100%', height: '100%', p: 2 }}>
                <MediaViewer step={currentStep} />
              </Box>
            ) : (
              // Display 3D model if no video/image - UPDATED TO MATCH STEPEDITOR
              <Canvas
                camera={{
                  position: [0, 0, 5], // Default position, will be overridden by CameraController
                  fov: 60
                }}
                style={{ height: '100%', width: '100%' }}
                frameloop="demand"
                gl={{ antialias: true, alpha: true }}
                onCreated={({ gl }) => {
                  gl.setClearColor('#f5f5f5', 1);
                }}
              >
                {/* Complete lighting setup - matching StepEditor exactly */}
                <ambientLight intensity={0.6} />
                <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
                <directionalLight position={[-10, -10, -5]} intensity={0.8} />
                <directionalLight position={[0, 10, 10]} intensity={0.8} />
                <pointLight position={[0, 10, 0]} intensity={0.7} />
                <pointLight position={[5, 0, 5]} intensity={0.5} />
                <pointLight position={[-5, 0, -5]} intensity={0.5} />

                {/* OrbitControls - placed at the correct position with ref */}
                <OrbitControls
                  ref={controlsRef}
                  enablePan={true}
                  enableZoom={true}
                  enableRotate={true}
                  enableDamping={true}
                  dampingFactor={0.05}
                  rotateSpeed={1.0}
                  zoomSpeed={1.5}
                  panSpeed={0.8}
                  autoRotate={false}
                />

                {/* Camera controller for step-specific camera settings */}
                <CameraController cameraSettings={currentStep?.camera} controlsRef={controlsRef} />

                {/* 3D Model with proper Suspense fallback */}
                <Suspense fallback={<primitive object={new THREE.Object3D()} />}>
                  <ModelWithMemo
                    key={module.modelUrl}
                    modelUrl={module.modelUrl}
                    modelType={module.modelType || "auto"}
                  />
                </Suspense>

                {/* Render hotspots separately to avoid model reload */}
                <HotspotsRenderer
                  hotspots={currentStep && currentStep.hotspots ? currentStep.hotspots : []}
                  onHotspotClick={handleHotspotClick}
                />
              </Canvas>
            )}
          </Paper>

          {/* Steps */}
          <Paper sx={{ p: 3, width: { xs: '100%', md: '40%' }, overflow: 'auto', maxHeight: '70vh' }}>
            <Stepper activeStep={activeStep} orientation="vertical">
              {steps.map((step, index) => (
                <Step key={step.id}>
                  <StepLabel>{step.title}</StepLabel>
                  <StepContent>
                    <Typography>{step.content}</Typography>
                    
                    {/* Media type indicator */}
                    {(step.videoUrl || step.imageUrl) && (
                      <Box sx={{ mt: 1, mb: 2 }}>
                        <Typography variant="caption" color="primary" sx={{ 
                          bgcolor: 'primary.main', 
                          color: 'white', 
                          px: 1, 
                          py: 0.5, 
                          borderRadius: 1,
                          fontSize: '0.75rem'
                        }}>
                          {step.videoUrl ? '🎬 Video Content' : '🖼️ Image Content'}
                        </Typography>
                      </Box>
                    )}

                    <Box sx={{ mb: 2, mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {index === steps.length - 1 ? (
                        moduleCompleted ? (
                          <>
                            <Button
                              variant="contained"
                              color="success"
                              disabled
                              endIcon={<CheckCircleOutlineIcon />}
                            >
                              Module Completed
                            </Button>
                            {nextModule && (
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={handleNextModule}
                                endIcon={<ArrowForwardIcon />}
                                sx={{ ml: 1 }}
                              >
                                Next Module
                              </Button>
                            )}
                          </>
                        ) : (
                          // Check if module has quiz
                          hasQuiz ? (
                            <Button
                              variant="contained"
                              color="success"
                              onClick={() => navigate(`/module/${moduleId}/quiz`)}
                              endIcon={<ArrowForwardIcon />}
                              disabled={quizLoading}
                            >
                              {quizLoading ? 'Checking...' : 'Take Quiz'}
                            </Button>
                          ) : (
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={handleCompleteModule}
                              endIcon={<CheckCircleOutlineIcon />}
                              disabled={quizLoading}
                            >
                              {quizLoading ? 'Checking...' : 'Complete Module'}
                            </Button>
                          )
                        )
                      ) : (
                        <Button
                          variant="contained"
                          onClick={handleNext}
                          endIcon={<ArrowForwardIcon />}
                        >
                          Continue
                        </Button>
                      )}
                      <Button
                        disabled={index === 0}
                        onClick={handleBack}
                        variant="outlined"
                        startIcon={<ArrowBackIcon />}
                      >
                        Back
                      </Button>
                    </Box>
                  </StepContent>
                </Step>
              ))}
            </Stepper>
          </Paper>
        </Box>

        {/* Snackbar for global messages */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleSnackbarClose} severity="info" sx={{ width: '100%' }}>
            {snackbarMessage}
          </Alert>
        </Snackbar>

        {/* Content Guide for question-3d module */}
        <ContentGuide
          open={showContentGuide}
          onClose={() => setShowContentGuide(false)}
          moduleId={moduleId}
        />
      </Container>
    </MainLayout>
  );
}

export default ModuleViewer;
