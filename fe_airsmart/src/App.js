import * as React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

import Homepage from './page/Homepage/Homepage';
import Signin from './page/Sigin/Signin';
import ModuleViewer from './page/ModuleViewer/ModuleViewer';
import ModuleQuiz from './page/ModuleQuiz/ModuleQuiz';
import ResetPassword from './page/ResetPassword/ResetPassword';
import RequireAuth from './auth/RequireAuth';

export default function App() {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Signin />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route
          path="/"
          element={
            <RequireAuth>
              <Homepage />
            </RequireAuth>
          }
        />
        <Route
          path="/module/:moduleId"
          element={
            <RequireAuth>
              <ModuleViewer />
            </RequireAuth>
          }
        />
        <Route
          path="/module/:moduleId/quiz"
          element={
            <RequireAuth>
              <ModuleQuiz />
            </RequireAuth>
          }
        />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}
