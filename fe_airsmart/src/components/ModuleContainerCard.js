import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typo<PERSON>,
  Box,
  Chip,
  LinearProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  IconButton,
  Divider
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Close as CloseIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Timer as TimerIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useUserProgress } from '../hooks/useUserProgress';
import { getSafeImageUrl } from '../utils/imageHelper';

const ModuleContainerCard = ({ container, onClick }) => {
  const navigate = useNavigate();
  const { userProgress: moduleProgress } = useUserProgress();
  const [dialogOpen, setDialogOpen] = useState(false);

  // Add safety checks for moduleProgress
  const safeModuleProgress = moduleProgress || {};

  // Calculate progress statistics
  const completedModules = container.getCompletedModulesCount(safeModuleProgress);
  const overallProgress = container.getOverallProgress(safeModuleProgress);
  const isCompleted = container.isCompleted(safeModuleProgress);

  const handleCardClick = () => {
    // Don't open dialog if container is locked
    if (container.isLocked) {
      return;
    }
    setDialogOpen(true);
  };

  const handleModuleClick = (module, index) => {

    // Check if user is logged in
    if (!moduleProgress) {
      setDialogOpen(false);
      // Could redirect to login page or show message
      return;
    }

    // For modules in container, check if previous module is completed
    if (index > 0) {
      const previousModule = container.modules[index - 1];
      if (previousModule) {
        const previousProgress = moduleProgress[previousModule.id];
        const isPreviousCompleted = previousProgress?.completed === true;


        if (!isPreviousCompleted) {
          setDialogOpen(false);
          // Show error message or prevent navigation
          return;
        }
      }
    }

    setDialogOpen(false);
    navigate(`/module/${module.id}`);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const getModuleStatus = (module, index) => {
    // Add null checking for moduleProgress
    if (!moduleProgress || !module || !module.id) {
      return 'not-started';
    }

    // For modules in container, check sequential unlocking
    if (index > 0) {
      const previousModule = container.modules[index - 1];
      if (previousModule) {
        const previousProgress = moduleProgress[previousModule.id];
        const isPreviousCompleted = previousProgress?.completed === true;

        if (!isPreviousCompleted) {
          return 'locked';
        }
      }
    }

    const progress = moduleProgress[module.id];

    // Check if module is completed
    if (progress?.completed) {
      return 'completed';
    }

    // Check if module is in progress (has currentStepId but not completed)
    const hasStarted = progress?.currentStepId || progress?.stepId;
    if (hasStarted && hasStarted !== 'completed') {
      return 'in-progress';
    }

    // Default to not-started
    return 'not-started';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon color="success" />;
      case 'in-progress':
        return <PlayArrowIcon color="primary" />;
      case 'locked':
        return <LockIcon color="disabled" />;
      default:
        return <RadioButtonUncheckedIcon color="disabled" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'locked':
        return 'Locked';
      default:
        return 'Start';
    }
  };

  return (
    <>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: container.isLocked ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease-in-out',
          opacity: container.isLocked ? 0.7 : 1,
          filter: container.isLocked ? 'grayscale(50%)' : 'none',
          '&:hover': {
            transform: container.isLocked ? 'none' : 'translateY(-4px)',
            boxShadow: container.isLocked ? 1 : 4,
          },
          border: '1px solid',
          borderColor: container.isLocked ? 'grey.400' : (isCompleted ? 'success.main' : 'divider'),
          position: 'relative'
        }}
        onClick={handleCardClick}
      >
        {/* Container Badge */}
        <Box sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>
          <Chip
            label={container.isComingSoon ? "Coming Soon" : "Course"}
            size="small"
            color={container.isLocked ? "warning" : "primary"}
            variant="filled"
            sx={{ fontWeight: 'bold' }}
          />
        </Box>

        {/* Lock Icon Overlay */}
        {container.isLocked && (
          <Box sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 10,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderRadius: '50%',
            width: 80,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <LockIcon sx={{ fontSize: 40, color: 'white' }} />
          </Box>
        )}

        {/* Thumbnail */}
        {container.thumbnail && (
          <Box sx={{ height: 200, overflow: 'hidden' }}>
            <img
              src={getSafeImageUrl(container.thumbnail)}
              alt={container.name}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </Box>
        )}

        <CardContent sx={{ flexGrow: 1, p: 3 }}>
          {/* Title and Description */}
          <Typography variant="h6" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
            {container.name}
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.5 }}>
            {container.description}
          </Typography>

          {/* Progress - Only show if not locked */}
          {!container.isLocked && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Progress
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {completedModules}/{container.totalModules} modules
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={overallProgress}
                color={isCompleted ? 'success' : 'primary'}
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                {Math.round(overallProgress)}% complete
              </Typography>
            </Box>
          )}

          {/* Statistics */}
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SchoolIcon fontSize="small" color={container.isLocked ? "disabled" : "primary"} />
              <Typography variant="caption" color="text.secondary">
                {container.totalModules} {container.totalModules === 0 ? "modules (TBA)" : "modules"}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <AssignmentIcon fontSize="small" color={container.isLocked ? "disabled" : "primary"} />
              <Typography variant="caption" color="text.secondary">
                {container.totalQuizQuestions} {container.totalQuizQuestions === 0 ? "quizzes (TBA)" : "quizzes"}
              </Typography>
            </Box>
          </Box>

          {/* Tags */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {container.tags.slice(0, 3).map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                variant="outlined"
                color={container.isLocked ? "default" : "primary"}
              />
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Module List Dialog - Only show if not locked */}
      {!container.isLocked && (
        <Dialog
          open={dialogOpen}
          onClose={handleDialogClose}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: { borderRadius: 2 }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold' }}>
                  {container.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                  {completedModules}/{container.totalModules} modules completed • {Math.round(overallProgress)}% progress
                </Typography>
              </Box>
              <IconButton onClick={handleDialogClose}>
                <CloseIcon />
              </IconButton>
            </Box>
            <LinearProgress
              variant="determinate"
              value={overallProgress}
              color={isCompleted ? 'success' : 'primary'}
              sx={{ mt: 2, height: 6, borderRadius: 3 }}
            />
          </DialogTitle>

          <DialogContent sx={{ px: 0 }}>
            <List disablePadding>
              {container.modules.map((module, index) => {
                const status = getModuleStatus(module, index);
                const isLastItem = index === container.modules.length - 1;

                return (
                  <React.Fragment key={module.id}>
                    <ListItem disablePadding>
                      <ListItemButton
                        onClick={() => handleModuleClick(module, index)}
                        disabled={status === 'locked'}
                        sx={{
                          px: 3,
                          py: 2,
                          cursor: status === 'locked' ? 'not-allowed' : 'pointer',
                          opacity: status === 'locked' ? 0.5 : 1,
                          '&.Mui-disabled': {
                            opacity: 0.5,
                            cursor: 'not-allowed',
                            '&:hover': {
                              backgroundColor: 'transparent',
                            }
                          }
                        }}
                      >
                        <ListItemIcon>
                          {getStatusIcon(status)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: 'medium',
                                color: status === 'locked' ? 'text.disabled' : 'text.primary'
                              }}
                            >
                              Module {module.order}: {module.name}
                            </Typography>
                          }
                          secondary={
                            <Box sx={{ mt: 0.5 }}>
                              <Typography
                                variant="body2"
                                component="span"
                                color={status === 'locked' ? 'text.disabled' : 'text.secondary'}
                                sx={{ mb: 1 }}
                              >
                                {status === 'locked' ? 'Complete previous modules to unlock' : module.description}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  <TimerIcon fontSize="small" color={status === 'locked' ? 'disabled' : 'disabled'} />
                                  <Typography
                                    variant="caption"
                                    component="span"
                                    color={status === 'locked' ? 'text.disabled' : 'text.secondary'}
                                  >
                                    {module.totalSteps} topics
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  <AssignmentIcon fontSize="small" color={status === 'locked' ? 'disabled' : 'disabled'} />
                                  <Typography
                                    variant="caption"
                                    component="span"
                                    color={status === 'locked' ? 'text.disabled' : 'text.secondary'}
                                  >
                                    {module.totalQuizQuestions || 0} quizzes
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                          }
                          secondaryTypographyProps={{ component: 'div' }}
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={getStatusLabel(status)}
                            size="small"
                            color={getStatusColor(status)}
                            variant={status === 'completed' ? 'filled' : 'outlined'}
                            sx={{
                              opacity: status === 'locked' ? 0.7 : 1
                            }}
                          />
                          {status !== 'locked' && <PlayArrowIcon color="primary" />}
                          {status === 'locked' && <LockIcon color="disabled" />}
                        </Box>
                      </ListItemButton>
                    </ListItem>
                    {!isLastItem && <Divider />}
                  </React.Fragment>
                );
              })}
            </List>
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button onClick={handleDialogClose} variant="outlined">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
};

export default ModuleContainerCard;