import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  Snackbar,
  Tooltip,
  InputAdornment,
  Stack,
  CircularProgress,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  Category as CategoryIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  FirstPage as FirstPageIcon,
  LastPage as LastPageIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Visibility as VisibilityIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import adminModuleService from '../services/adminModuleService';
import ModuleCourseManager from '../components/ModuleCourseManager';
import { convertToSignedUrl } from '../utils/wasabiHelper';

function CourseManagement() {
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
  });

  // File upload states for thumbnail
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState('');
  const [uploadingThumbnail, setUploadingThumbnail] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const thumbnailInputRef = useRef(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    thumbnail: '',
    tags: [],
    isActive: true,
    isLocked: false,
    order: 1,
  });

  const [openModulesDialog, setOpenModulesDialog] = useState(false);
  const [selectedCourseForModules, setSelectedCourseForModules] = useState(null);

  const loadCourses = useCallback(async () => {
    try {
      setLoading(true);

      const response = await adminModuleService.getCourses();
      const coursesData = response?.data || response || [];

      // Process courses to add signed URLs for thumbnails
      const processedCourses = coursesData.map(course => ({
        ...course,
        thumbnail: course.thumbnail ? convertToSignedUrl(course.thumbnail) : course.thumbnail
      }));

      // Apply filters
      let filtered = processedCourses;

      if (searchTerm) {
        filtered = filtered.filter(course =>
          course.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          course.description?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      if (statusFilter !== 'all') {
        filtered = filtered.filter(course =>
          statusFilter === 'active' ? course.isActive : !course.isActive
        );
      }

      // Sort by order
      filtered.sort((a, b) => (a.order || 0) - (b.order || 0));

      setCourses(processedCourses);
      setFilteredCourses(filtered);

      // Calculate stats
      setStats({
        total: processedCourses.length,
        active: processedCourses.filter(cat => cat.isActive).length,
        inactive: processedCourses.filter(cat => !cat.isActive).length,
      });

    } catch (error) {
      console.error('Error loading courses:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load courses. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter]);

  useEffect(() => {
    loadCourses();
  }, [loadCourses]);

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setPage(0);
  };

  const handleOpenDialog = (course = null) => {
    if (course) {
      setEditingCourse(course);
      setFormData({
        name: course.name || '',
        description: course.description || '',
        thumbnail: course.thumbnail || '',
        tags: course.tags || [],
        isActive: course.isActive !== false,
        isLocked: course.isLocked === true,
        order: course.order || 1,
      });
      setThumbnailPreview(course.thumbnail || '');
    } else {
      setEditingCourse(null);
      setFormData({
        name: '',
        description: '',
        thumbnail: '',
        tags: [],
        isActive: true,
        isLocked: false,
        order: Math.max(...courses.map(c => c.order || 0), 0) + 1,
      });
      setThumbnailPreview('');
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingCourse(null);
    setThumbnailFile(null);
    setThumbnailPreview('');
  };

  const handleSaveCourse = async () => {
    try {
      setLoading(true);

      let thumbnailUrl = formData.thumbnail; // URL cũ nếu có

      // Upload thumbnail if a new file is selected
      if (thumbnailFile) {
        const formDataThumbnail = new FormData();
        formDataThumbnail.append('file', thumbnailFile);

        setUploadingThumbnail(true);
        try {
          const uploadResponse = await adminModuleService.uploadThumbnail(formDataThumbnail, (progress) => {
            setUploadProgress(Math.round((progress.loaded / progress.total) * 100));
          });

          // Lấy URL từ response
          thumbnailUrl = uploadResponse?.data?.url || uploadResponse?.url;
          console.log('✅ Thumbnail uploaded, using signed URL for storage:', thumbnailUrl);

        } catch (uploadError) {
          console.error('❌ Error uploading thumbnail:', uploadError);
          setSnackbar({
            open: true,
            message: 'Failed to upload thumbnail: ' + uploadError.message,
            severity: 'error'
          });
          return; // Dừng lại nếu upload thất bại
        } finally {
          setUploadingThumbnail(false);
          setUploadProgress(0);
        }
      } else {
        // Nếu không upload file mới, chuyển signed URL về URL gốc
        if (thumbnailUrl && thumbnailUrl.includes('AWSAccessKeyId')) {
          // Extract object key from signed URL
          const urlObj = new URL(thumbnailUrl);
          const pathParts = urlObj.pathname.split('/');
          pathParts.shift(); // Remove empty first element
          pathParts.shift(); // Remove bucket name
          const objectKey = pathParts.join('/');

          // Decode URL encoding (convert %2F back to /)
          const decodedObjectKey = decodeURIComponent(objectKey);

          // Tạo URL gốc từ object key đã decode
          thumbnailUrl = `https://s3.ap-southeast-2.wasabisys.com/airsmart/${decodedObjectKey}`;
          console.log('✅ Converting signed URL to original URL for storage:', thumbnailUrl);
        }
      }

      // Tạo courseData với thumbnail URL
      const courseData = {
        name: formData.name,
        description: formData.description,
        thumbnail: thumbnailUrl, // Đảm bảo có thumbnail URL
        tags: formData.tags,
        isActive: formData.isActive,
        isLocked: formData.isLocked,
        order: formData.order,
      };

      console.log('🔄 Saving course with data:', courseData);

      if (editingCourse) {
        // Update existing course
        await adminModuleService.updateCourse(editingCourse.id, courseData);
        setSnackbar({
          open: true,
          message: 'Course updated successfully!',
          severity: 'success'
        });
      } else {
        // Add new course
        await adminModuleService.createCourse(courseData);
        setSnackbar({
          open: true,
          message: 'Course created successfully!',
          severity: 'success'
        });
      }

      handleCloseDialog();
      loadCourses(); // Reload courses after save

    } catch (error) {
      console.error('Save error:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save course. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      setUploadingThumbnail(false);
      setUploadProgress(0);
    }
  };

  const handleOpenMenu = (event, course) => {
    setAnchorEl(event.currentTarget);
    setSelectedCourse(course);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedCourse(null);
  };

  const exportCourses = () => {
    const csvContent = [
      ['ID', 'Name', 'Description', 'Module Count', 'Status', 'Order', 'Created At'],
      ...filteredCourses.map(course => [
        course.id,
        course.name || '',
        course.description || '',
        course.moduleCount || 0,
        course.isActive ? 'Publish' : 'Inactive',
        course.order || 0,
        course.createdAt || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `courses_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const handleOpenDeleteDialog = (course) => {
    setCourseToDelete(course);
    setOpenDeleteDialog(true);
    handleCloseMenu();
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setCourseToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (!courseToDelete) return;

    try {
      await adminModuleService.deleteCourse(courseToDelete.id);
      setSnackbar({
        open: true,
        message: 'Course deleted successfully!',
        severity: 'info'
      });
      handleCloseDeleteDialog();
      loadCourses();
    } catch (error) {
      console.error('Delete error:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to delete course. Please try again.',
        severity: 'error'
      });
    }
  };

  const handleTagsChange = (e) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
    setFormData({ ...formData, tags: tagsArray });
  };

  const handleOpenModulesManager = (course) => {
    setSelectedCourseForModules(course);
    setOpenModulesDialog(true);
    handleCloseMenu();
  };

  const handleCloseModulesManager = () => {
    setOpenModulesDialog(false);
    setSelectedCourseForModules(null);
    // Reload courses to update module counts
    loadCourses();
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1, color: 'primary.main' }}>
            Course Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage module courses and organization
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadCourses}
            disabled={loading}
            sx={{ borderRadius: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={exportCourses}
            disabled={filteredCourses.length === 0}
            sx={{ borderRadius: 2 }}
          >
            Export CSV
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{ borderRadius: 2, px: 3 }}
          >
            Add New Course
          </Button>
        </Stack>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3, boxShadow: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CategoryIcon color="primary" />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }} color="text.primary">Filters & Search</Typography>
          </Box>

          {/* Stats Display */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Chip
              label={`Total: ${stats.total}`}
              variant="outlined"
              size="small"
            />
            <Chip
              label={`Publish: ${stats.active}`}
              color="success"
              variant="outlined"
              size="small"
            />
            <Chip
              label={`Inactive: ${stats.inactive}`}
              color="default"
              variant="outlined"
              size="small"
            />
          </Box>
        </Box>

        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              placeholder="Search by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              fullWidth
              size="medium"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 3,
                  backgroundColor: 'action.hover',
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="medium">
              <InputLabel>Filter by Status</InputLabel>
              <Select
                value={statusFilter}
                label="Filter by Status"
                onChange={(e) => setStatusFilter(e.target.value)}
                sx={{
                  borderRadius: 3,
                  backgroundColor: 'action.hover',
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                }}
              >
                <MenuItem value="all">All Courses</MenuItem>
                <MenuItem value="active">Publish Only</MenuItem>
                <MenuItem value="inactive">Inactive Only</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            {(searchTerm || statusFilter !== 'all') && (
              <Button
                size="medium"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                variant="outlined"
                color="warning"
                sx={{
                  borderRadius: 2,
                  height: 56, // Match TextField height
                  '&:hover': {
                    backgroundColor: 'warning.light'
                  }
                }}
              >
                Clear Filters
              </Button>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Courses Table */}
      <TableContainer component={Paper} sx={{ borderRadius: 3, boxShadow: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'action.hover' }}>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Course</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Description</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Modules</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Access</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Order</TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Created</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Loading courses...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : filteredCourses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                  <CategoryIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No courses found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Try adjusting your search filters'
                      : 'Start by adding your first course'
                    }
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              filteredCourses
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((course) => (
                  <TableRow key={course.id} hover sx={{
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    }
                  }}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{
                          bgcolor: course.isActive ? 'primary.main' : 'grey.500',
                          color: 'white'
                        }}>
                          {(course.name || 'C').charAt(0).toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }} color="text.primary">
                            {course.name || 'Unnamed Course'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {course.id?.substring(0, 8)}...
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.primary" sx={{
                        maxWidth: 200,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {course.description || 'No description'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${course.moduleCount || 0} modules`}
                        size="small"
                        color={course.moduleCount > 0 ? 'primary' : 'default'}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={course.isActive ? 'Publish' : 'Inactive'}
                        color={course.isActive ? 'success' : 'default'}
                        size="small"
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={course.isLocked ? 'Coming Soon' : 'Unlocked'}
                        color={course.isLocked ? 'error' : 'default'}
                        size="small"
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.primary">
                        {course.order || 0}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.primary">
                        {formatDate(course.createdAt)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          onClick={(e) => handleOpenMenu(e, course)}
                          size="small"
                          sx={{
                            '&:hover': {
                              backgroundColor: 'action.hover',
                              color: 'primary.main'
                            }
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredCourses.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => handleOpenDialog(selectedCourse)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Course</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleOpenModulesManager(selectedCourse)}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            <Box>
              <Typography variant="body2">
                Manage Modules ({selectedCourse?.moduleCount || 0})
              </Typography>
              <Typography variant="caption" color="text.secondary">
                View, reorder and delete modules
              </Typography>
            </Box>
          </ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={() => handleOpenDeleteDialog(selectedCourse)}
          sx={{ color: 'error.main' }}
          disabled={selectedCourse?.moduleCount > 0}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Course</ListItemText>
        </MenuItem>
      </Menu>

      {/* Add/Edit Course Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingCourse ? 'Edit Course' : 'Add New Course'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Course Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              multiline
              rows={3}
              fullWidth
              required
            />

            {/* Thumbnail Upload */}
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Thumbnail (Image URL or Upload)
              </Typography>
              <Button
                variant="outlined"
                component="label"
                size="small"
                startIcon={<CloudUploadIcon />}
                sx={{
                  borderRadius: 2,
                  width: '100%',
                  justifyContent: 'flex-start',
                  ...(thumbnailFile && {
                    backgroundColor: 'action.selected',
                    borderColor: 'primary.main',
                    color: 'primary.main',
                  })
                }}
              >
                {thumbnailFile ? 'Change Thumbnail' : 'Upload Thumbnail'}
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      setThumbnailFile(file);
                      setThumbnailPreview(URL.createObjectURL(file));
                    }
                  }}
                  hidden
                  ref={thumbnailInputRef}
                />
              </Button>

              {thumbnailPreview && (
                <Box sx={{ mt: 2, position: 'relative' }}>
                  <img
                    src={thumbnailPreview}
                    alt="Thumbnail Preview"
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: 8,
                      border: '1px solid',
                      borderColor: 'action.hover',
                    }}
                  />
                  <IconButton
                    size="small"
                    onClick={() => {
                      setThumbnailFile(null);
                      setThumbnailPreview('');
                    }}
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      bgcolor: 'background.paper',
                      '&:hover': {
                        bgcolor: 'error.light',
                      }
                    }}
                  >
                    <ClearIcon color="error" />
                  </IconButton>
                </Box>
              )}

              {uploadingThumbnail && (
                <Box sx={{ mt: 1 }}>
                  <LinearProgress variant="determinate" value={uploadProgress} />
                </Box>
              )}
            </Box>

            <TextField
              label="Tags (comma separated)"
              value={formData.tags.join(', ')}
              onChange={handleTagsChange}
              fullWidth
              placeholder="e.g. installation, advanced, basic"
            />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  label="Order"
                  type="number"
                  value={formData.order}
                  onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 1 })}
                  fullWidth
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    />
                  }
                  label="Publish"
                  sx={{ mt: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isLocked}
                      onChange={(e) => setFormData({ ...formData, isLocked: e.target.checked })}
                    />
                  }
                  label="Coming Soon"
                  sx={{ mt: 1 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSaveCourse}
            variant="contained"
            disabled={!formData.name || !formData.description}
          >
            {editingCourse ? 'Update Course' : 'Create Course'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-confirmation-dialog"
      >
        <DialogTitle id="delete-confirmation-dialog">
          Confirm Deletion
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Are you sure you want to delete the course "{courseToDelete?.name}"?
          </Typography>
          {courseToDelete?.moduleCount > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              This course contains {courseToDelete.moduleCount} modules. Please move or delete modules first.
            </Alert>
          )}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={courseToDelete?.moduleCount > 0}
          >
            Delete Course
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          severity={snackbar.severity}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Module Course Manager Dialog */}
      <Dialog
        open={openModulesDialog}
        onClose={handleCloseModulesManager}
        maxWidth={false}
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            width: '90vw',
            height: '85vh',
            maxWidth: 'none',
            maxHeight: 'none',
            borderRadius: 1
          },
        }}
      >
        <DialogContent sx={{ p: 0, height: '100%' }}>
          {selectedCourseForModules && (
            <ModuleCourseManager
              courseId={selectedCourseForModules.id}
              courseName={selectedCourseForModules.name}
              onClose={handleCloseModulesManager}
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
}

export default CourseManagement;