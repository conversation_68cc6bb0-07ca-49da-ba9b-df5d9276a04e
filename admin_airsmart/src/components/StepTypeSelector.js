import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Divider,
} from '@mui/material';
import {
  ViewInAr as Model3DIcon,
  VideoLibrary as VideoIcon,
  Image as ImageIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

function StepTypeSelector({ open, onClose, onSelectType, moduleData }) {
  const stepTypes = [
    {
      type: '3d-model',
      title: '3D Model Step',
      description: 'Interactive 3D model with hotspots, camera positions, and actions',
      icon: Model3DIcon,
      color: '#1976d2',
      features: [
        'Interactive 3D model',
        'Hotspots & annotations',
        'Camera positioning',
        'Automated actions',
        'Guided tours'
      ],
      requiresModel: true // This step type requires a 3D model
    },
    {
      type: 'video',
      title: 'Video Step',
      description: 'Video-based learning with playback controls and annotations',
      icon: VideoIcon,
      color: '#d32f2f',
      features: [
        'Video playback',
        'Timeline controls',
        'Video annotations',
        'Subtitles support',
        'Progress tracking'
      ],
      requiresModel: false
    },
    {
      type: 'image',
      title: 'Image Step',
      description: 'Static image with interactive annotations and text overlays',
      icon: ImageIcon,
      color: '#388e3c',
      features: [
        'High-quality images',
        'Zoom & pan controls',
        'Text annotations',
        'Hotspot markers',
        'Image galleries'
      ],
      requiresModel: false
    }
  ];

  // Check if module has 3D model
  const hasModel3D = moduleData?.has3DModel || moduleData?.modelUrl || moduleData?.model3DUrl;

  const handleSelectType = (type) => {
    onSelectType(type);
    onClose();
  };

  const isStepDisabled = (stepType) => {
    return stepType.requiresModel && !hasModel3D;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: '1200px',
          minHeight: '600px',
        }
      }}
    >
      <DialogTitle sx={{ 
        borderBottom: '1px solid', 
        borderColor: 'divider', 
        pb: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Box>
          <Typography variant="h5" component="div" gutterBottom>
            Choose Step Type
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Select the type of step you want to create for your module
          </Typography>
          {/* {!hasModel3D && (
            <Typography variant="caption" color="warning.main" sx={{ display: 'block', mt: 1 }}>
              ⚠️ This module doesn't have a 3D model. Add a 3D model to enable 3D Model Steps.
            </Typography>
          )} */}
        </Box>
        <Button
          onClick={onClose}
          size="small"
          sx={{ minWidth: 'auto', p: 1 }}
        >
          <CloseIcon />
        </Button>
      </DialogTitle>
      
      <DialogContent sx={{ 
        p: { xs: 2, md: 4 }, 
        '&.MuiDialogContent-root': {
          paddingTop: { xs: '16px !important', md: '32px !important' }
        }
      }}>
        <Box 
          sx={{ 
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 2, md: 3 },
            justifyContent: 'center',
            alignItems: 'stretch',
            minHeight: '400px'
          }}
        >
          {stepTypes.map((stepType) => {
            const IconComponent = stepType.icon;
            const isDisabled = isStepDisabled(stepType);
            
            return (
              <Box 
                key={stepType.type}
                sx={{ 
                  flex: { xs: 'none', md: '1' },
                  maxWidth: { xs: '100%', md: '350px' },
                  minWidth: { xs: '100%', md: '300px' }
                }}
              >
                <Card 
                  sx={{ 
                    height: '100%',
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    border: '2px solid',
                    borderColor: 'transparent',
                    opacity: isDisabled ? 0.6 : 1,
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                    background: isDisabled 
                      ? 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)'
                      : `linear-gradient(135deg, ${stepType.color}08 0%, ${stepType.color}15 100%)`,
                    '&:hover': !isDisabled ? {
                      borderColor: stepType.color,
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: `0 16px 40px ${stepType.color}25, 0 8px 16px rgba(0,0,0,0.1)`,
                      '& .step-icon': {
                        transform: 'scale(1.1) rotate(5deg)',
                      },
                      '& .step-button': {
                        background: `linear-gradient(45deg, ${stepType.color} 30%, ${stepType.color}dd 90%)`,
                        transform: 'translateY(-2px)',
                      }
                    } : {},
                    ...(isDisabled && {
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.03) 10px, rgba(0,0,0,0.03) 20px)',
                        borderRadius: 'inherit',
                        zIndex: 1
                      }
                    })
                  }}
                  onClick={!isDisabled ? () => handleSelectType(stepType.type) : undefined}
                >
                  <CardContent sx={{ 
                    p: { xs: 2, md: 3 }, 
                    textAlign: 'center', 
                    position: 'relative', 
                    zIndex: 2,
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2
                  }}>
                    {isDisabled && (
                      <Box sx={{ 
                        position: 'absolute', 
                        top: 12, 
                        right: 12,
                        bgcolor: 'warning.main',
                        color: 'white',
                        borderRadius: '50%',
                        width: 28,
                        height: 28,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px',
                        fontWeight: 'bold',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                        zIndex: 3
                      }}>
                        !
                      </Box>
                    )}
                    
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'center',
                      alignItems: 'center',
                      minHeight: '80px'
                    }}>
                      <IconComponent 
                        className="step-icon"
                        sx={{ 
                          fontSize: { xs: 48, md: 56 }, 
                          color: isDisabled ? 'action.disabled' : stepType.color,
                          transition: 'all 0.3s ease',
                          filter: isDisabled ? 'grayscale(100%)' : 'none',
                          dropShadow: !isDisabled ? `0 4px 8px ${stepType.color}30` : 'none'
                        }} 
                      />
                    </Box>
                    
                    <Box sx={{ flex: 1 }}>
                      <Typography 
                        variant="h6" 
                        gutterBottom
                        sx={{ 
                          fontWeight: 700,
                          color: isDisabled ? 'text.disabled' : 'text.primary',
                          fontSize: { xs: '1.1rem', md: '1.25rem' },
                          lineHeight: 1.3
                        }}
                      >
                        {stepType.title}
                      </Typography>
                      
                      <Typography 
                        variant="body2" 
                        color={isDisabled ? 'text.disabled' : 'text.secondary'}
                        sx={{ 
                          mb: 2, 
                          minHeight: '60px',
                          lineHeight: 1.5,
                          fontSize: { xs: '0.875rem', md: '0.9rem' }
                        }}
                      >
                        {isDisabled ? 'Requires 3D model to be added to the module first' : stepType.description}
                      </Typography>

                      <Divider sx={{ 
                        my: 2,
                        borderColor: isDisabled ? 'action.disabled' : `${stepType.color}20`
                      }} />

                      <Box sx={{ textAlign: 'left' }}>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            fontWeight: 700,
                            color: isDisabled ? 'text.disabled' : stepType.color,
                            textTransform: 'uppercase',
                            letterSpacing: 1,
                            fontSize: '0.75rem'
                          }}
                        >
                          Features:
                        </Typography>
                        <Box sx={{ mt: 1.5 }}>
                          {stepType.features.slice(0, 4).map((feature, index) => (
                            <Typography 
                              key={index}
                              variant="caption" 
                              sx={{ 
                                display: 'block',
                                color: isDisabled ? 'text.disabled' : 'text.secondary',
                                lineHeight: 1.6,
                                fontSize: '0.8rem',
                                mb: 0.5,
                                '&:before': {
                                  content: '"✓ "',
                                  color: isDisabled ? 'action.disabled' : stepType.color,
                                  fontWeight: 'bold',
                                  marginRight: '4px'
                                }
                              }}
                            >
                              {feature}
                            </Typography>
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                  
                  <CardActions sx={{ p: { xs: 2, md: 3 }, pt: 0, zIndex: 2 }}>
                    <Button
                      className="step-button"
                      variant="contained"
                      fullWidth
                      disabled={isDisabled}
                      sx={{
                        bgcolor: isDisabled ? 'action.disabledBackground' : stepType.color,
                        color: isDisabled ? 'text.disabled' : 'white',
                        py: 1.5,
                        fontSize: { xs: '0.875rem', md: '0.9rem' },
                        fontWeight: 600,
                        borderRadius: 2,
                        textTransform: 'none',
                        transition: 'all 0.3s ease',
                        boxShadow: !isDisabled ? `0 4px 12px ${stepType.color}30` : 'none',
                        '&:hover': !isDisabled ? {
                          bgcolor: stepType.color,
                          filter: 'brightness(0.9)',
                          boxShadow: `0 6px 16px ${stepType.color}40`
                        } : {},
                        '&.Mui-disabled': {
                          bgcolor: 'action.disabledBackground',
                          color: 'text.disabled',
                          boxShadow: 'none'
                        }
                      }}
                      onClick={!isDisabled ? () => handleSelectType(stepType.type) : undefined}
                    >
                      {isDisabled ? 'Requires 3D Model' : `Select ${stepType.title}`}
                    </Button>
                  </CardActions>
                </Card>
              </Box>
            );
          })}
        </Box>

        <Box sx={{ 
          mt: 4, 
          p: 3, 
          bgcolor: 'background.default', 
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider',
          background: 'linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%)'
        }}>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ lineHeight: 1.6 }}>
            💡 <strong>Tip:</strong> You can always change the step type later during editing. 
            Choose the type that best fits your content format.
            {!hasModel3D && (
              <Box component="span" sx={{ display: 'block', mt: 1, color: 'warning.main', fontWeight: 500 }}>
                🔧 To create 3D Model Steps, first add a 3D model to this module using the "Add 3D Model" button.
              </Box>
            )}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button 
          onClick={onClose}
          variant="outlined"
          sx={{ 
            minWidth: '120px',
            py: 1,
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500
          }}
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default StepTypeSelector;