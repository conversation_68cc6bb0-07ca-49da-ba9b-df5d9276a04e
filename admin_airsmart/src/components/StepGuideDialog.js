import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import TouchAppIcon from '@mui/icons-material/TouchApp';
import InfoIcon from '@mui/icons-material/Info';

function StepGuideDialog({ open, onClose }) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <InfoIcon color="primary" />
          Step Creation Guide
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Typography variant="body1" paragraph>
          Hướng dẫn tạo step cho module 3D với camera, hotspots và actions.
        </Typography>

        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CameraAltIcon color="primary" />
              <Typography variant="h6">Camera Position & Target</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body2">
                <strong>Camera Position [x, y, z]:</strong> Vị trí của camera trong không gian 3D
              </Typography>
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2">• <strong>x:</strong> Trái (-) / Phải (+)</Typography>
                <Typography variant="body2">• <strong>y:</strong> Dưới (-) / Trên (+)</Typography>
                <Typography variant="body2">• <strong>z:</strong> Gần (+) / Xa (-)</Typography>
              </Box>
              
              <Typography variant="body2">
                <strong>Camera Target [x, y, z]:</strong> Điểm mà camera nhìn vào
              </Typography>
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2">• Thường là [0, 0, 0] (trung tâm model)</Typography>
                <Typography variant="body2">• Có thể thay đổi để focus vào phần cụ thể</Typography>
              </Box>

              <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
                <Typography variant="body2" fontWeight="bold">Ví dụ:</Typography>
                <Typography variant="body2">Position: [2, 1, 4] - Camera ở phía phải, hơi trên, và gần model</Typography>
                <Typography variant="body2">Target: [0, 0, 0] - Nhìn vào trung tâm model</Typography>
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TouchAppIcon color="secondary" />
              <Typography variant="h6">Hotspots</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body2">
                Hotspots là các điểm tương tác trên model 3D mà người dùng có thể click để xem thông tin.
              </Typography>
              
              <Typography variant="body2"><strong>Thuộc tính chính:</strong></Typography>
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2">• <strong>Position [x, y, z]:</strong> Vị trí hotspot trên model</Typography>
                <Typography variant="body2">• <strong>Label:</strong> Tên hiển thị của hotspot</Typography>
                <Typography variant="body2">• <strong>Action:</strong> Hành động khi click (highlight, info, navigate)</Typography>
                <Typography variant="body2">• <strong>Description:</strong> Mô tả chi tiết</Typography>
              </Box>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip label="highlight" size="small" color="primary" />
                <Chip label="info" size="small" color="secondary" />
                <Chip label="navigate" size="small" color="success" />
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PlayArrowIcon color="success" />
              <Typography variant="h6">Actions (Hành động tự động)</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body2">
                Actions là các hành động tự động được thực hiện khi step được load.
              </Typography>
              
              <Typography variant="body2"><strong>Các loại action phổ biến:</strong></Typography>
              
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2" fontWeight="bold">1. Rotate (Xoay):</Typography>
                <Box sx={{ pl: 2, mb: 1 }}>
                  <Typography variant="body2">• Xoay model tự động</Typography>
                  <Typography variant="body2">• axis: [0, 1, 0] = xoay quanh trục Y</Typography>
                  <Typography variant="body2">• speed: 0.01 = tốc độ xoay chậm</Typography>
                </Box>

                <Typography variant="body2" fontWeight="bold">2. Zoom:</Typography>
                <Box sx={{ pl: 2, mb: 1 }}>
                  <Typography variant="body2">• Zoom vào/ra model</Typography>
                  <Typography variant="body2">• target: vị trí zoom đến</Typography>
                  <Typography variant="body2">• duration: thời gian zoom</Typography>
                </Box>

                <Typography variant="body2" fontWeight="bold">3. Highlight:</Typography>
                <Box sx={{ pl: 2, mb: 1 }}>
                  <Typography variant="body2">• Làm nổi bật phần cụ thể</Typography>
                  <Typography variant="body2">• color: màu highlight</Typography>
                  <Typography variant="body2">• intensity: độ sáng</Typography>
                </Box>
              </Box>

              <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
                <Typography variant="body2" fontWeight="bold">Ví dụ Action:</Typography>
                <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
{`{
  "type": "rotate",
  "params": {
    "axis": [0, 1, 0],
    "speed": 0.01
  },
  "delay": 1000,
  "duration": 5000
}`}
                </Typography>
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InfoIcon color="info" />
              <Typography variant="h6">IntroJS Steps</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body2">
                IntroJS tạo hướng dẫn tương tác từng bước cho người dùng.
              </Typography>
              
              <Typography variant="body2"><strong>Thuộc tính:</strong></Typography>
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2">• <strong>intro:</strong> Nội dung hướng dẫn</Typography>
                <Typography variant="body2">• <strong>element:</strong> CSS selector của element cần highlight</Typography>
                <Typography variant="body2">• <strong>position:</strong> Vị trí tooltip (top, bottom, left, right)</Typography>
              </Box>

              <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
                <Typography variant="body2" fontWeight="bold">Ví dụ:</Typography>
                <Typography variant="body2">
                  "intro": "Click vào đây để xem thông tin an toàn"<br/>
                  "element": "#hotspot-safety"<br/>
                  "position": "bottom"
                </Typography>
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ bgcolor: 'primary.light', p: 2, borderRadius: 1 }}>
          <Typography variant="body2" fontWeight="bold" color="primary.contrastText">
            💡 Mẹo:
          </Typography>
          <Typography variant="body2" color="primary.contrastText">
            • Sử dụng 3D viewer để tìm vị trí camera và hotspot phù hợp<br/>
            • Test step trước khi lưu để đảm bảo trải nghiệm tốt<br/>
            • Kết hợp camera, hotspots và actions để tạo step sinh động
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Đã hiểu
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default StepGuideDialog;
