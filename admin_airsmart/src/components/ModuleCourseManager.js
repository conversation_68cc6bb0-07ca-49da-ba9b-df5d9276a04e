import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Snackbar,
  Alert,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  DragIndicator as DragIndicatorIcon,
  Reorder as ReorderIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  ViewInAr as ViewInArIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import adminModuleService from '../services/adminModuleService';

// Sortable Module Item Component
function SortableModuleItem({ module, index, onEdit, onDelete, loading, isReorderMode, theme }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <ListItem
      ref={setNodeRef}
      style={style}
      sx={{
        border: '2px solid',
        borderColor: isDragging ? theme.palette.primary.main : 'divider',
        borderRadius: 2,
        mb: 1,
        bgcolor: isDragging ? alpha(theme.palette.primary.main, 0.05) : 'background.paper',
        cursor: isReorderMode ? 'grab' : 'default',
        transition: 'all 0.2s ease',
        '&:hover': isReorderMode ? {
          borderColor: theme.palette.primary.main,
          boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
        } : {},
        ...(isReorderMode && {
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: '4px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            borderRadius: '2px 0 0 2px',
          }
        })
      }}
      {...(isReorderMode ? { ...attributes, ...listeners } : {})}
    >
      {isReorderMode && (
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mr: 2,
          color: 'primary.main'
        }}>
          <DragIndicatorIcon />
        </Box>
      )}
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label={`#${module.courseOrder || index + 1}`} 
              size="small" 
              color="primary" 
              sx={{ fontWeight: 600 }}
            />
            <Typography variant="subtitle1" fontWeight={600}>
              {module.name || 'Untitled Module'}
            </Typography>
            <Chip 
              label={module.isLocked ? 'Coming Soon' : 'Available'} 
              size="small" 
              variant="outlined"
              color={module.isLocked ? 'error' : 'success'}
            />
            {isReorderMode && (
              <Chip 
                label="Drag to reorder" 
                size="small" 
                variant="filled"
                color="info"
                sx={{ ml: 'auto', fontSize: '0.75rem' }}
              />
            )}
          </Box>
        }
        secondary={
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {module.description ? 
                (module.description.length > 100 ? 
                  module.description.substring(0, 100) + '...' : 
                  module.description
                ) : 'No description'
              }
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip 
                icon={<AssignmentIcon sx={{ fontSize: '0.8rem' }} />}
                label={`${module.actualStepsCount || 0} Steps`} 
                size="small" 
                variant="outlined"
              />
              <Chip 
                icon={<QuizIcon sx={{ fontSize: '0.8rem' }} />}
                label={`${module.actualQuizCount || 0} Quiz`} 
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={`Updated: ${formatDate(module.lastUpdated)}`} 
                size="small" 
                variant="outlined"
              />
            </Box>
          </Box>
        }
      />
      
      {!isReorderMode && (
        <ListItemSecondaryAction>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => onEdit(module)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.2),
                }
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(module)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.error.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.error.main, 0.2),
                }
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </ListItemSecondaryAction>
      )}
    </ListItem>
  );
}

function ModuleCourseManager({ courseId, courseName, onClose }) {
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [moduleToDelete, setModuleToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [originalModules, setOriginalModules] = useState([]);
  const [reorderLoading, setReorderLoading] = useState(false);

  const theme = useTheme();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load modules for this course
  useEffect(() => {
    const loadModules = async () => {
      try {
        setLoading(true);
        // Get all modules and filter by course
        const allModules = await adminModuleService.getModules();
        const courseModules = allModules.filter(module => module.course === courseId);
        
        // Sort by courseOrder
        courseModules.sort((a, b) => (a.courseOrder || 0) - (b.courseOrder || 0));
        
        setModules(courseModules);
      } catch (error) {
        console.error('Error loading modules:', error);
        setSnackbar({
          open: true,
          message: `Error loading modules: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    if (courseId) {
      loadModules();
    }
  }, [courseId]);

  const handleEditModule = (module) => {
    // Save module ID to session storage for ModuleManagement to pick up
    sessionStorage.setItem('moduleManagement_editModuleId', module.id);
    sessionStorage.setItem('moduleManagement_courseFilter', courseId);
    sessionStorage.setItem('moduleManagement_searchTerm', '');
    
    // Navigate to ModuleManagement page
    window.location.href = '/modules'; // This will trigger a full page navigation
    
    setSnackbar({
      open: true,
      message: `Redirecting to edit module: ${module.name}`,
      severity: 'info'
    });
  };

  const handleDeleteModule = async () => {
    if (!moduleToDelete) return;

    try {
      setLoading(true);
      await adminModuleService.deleteModule(moduleToDelete.id);
      
      // Remove from local state
      setModules(modules.filter(m => m.id !== moduleToDelete.id));
      
      setSnackbar({
        open: true,
        message: 'Module deleted successfully!',
        severity: 'info'
      });
      
      setOpenDeleteDialog(false);
      setModuleToDelete(null);
    } catch (error) {
      console.error('Error deleting module:', error);
      setSnackbar({
        open: true,
        message: `Error deleting module: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle drag end for reordering
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setModules((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        const newOrder = arrayMove(items, oldIndex, newIndex);
        
        // Update courseOrder property for each module
        return newOrder.map((module, index) => ({
          ...module,
          courseOrder: index + 1
        }));
      });
    }
  };

  // Toggle reorder mode
  const handleToggleReorderMode = () => {
    if (!isReorderMode) {
      // Entering reorder mode - save original order
      setOriginalModules([...modules]);
      setIsReorderMode(true);
      setSnackbar({
        open: true,
        message: 'Reorder mode enabled. Drag modules to change their order.',
        severity: 'info'
      });
    } else {
      // Exiting reorder mode - restore original order
      setModules(originalModules);
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Reorder mode cancelled. Order restored.',
        severity: 'info'
      });
    }
  };

  // Save new order
  const handleSaveOrder = async () => {
    try {
      console.log('🔄 Starting handleSaveOrder for modules...');
      console.log('📋 Current modules:', modules.map(m => ({ id: m.id, name: m.name, courseOrder: m.courseOrder })));
      
      setReorderLoading(true);
      
      // Update courseOrder for each module
      const updatePromises = modules.map((module, index) => {
        const newCourseOrder = index + 1;
        console.log(`🔄 Processing module ${module.id}: current order ${module.courseOrder} -> new order ${newCourseOrder}`);
        
        // Update module with new courseOrder
        return adminModuleService.updateModule(module.id, {
          courseOrder: newCourseOrder
        });
      });

      console.log('🚀 Calling API for all modules...');
      await Promise.all(updatePromises);
      
      console.log('✅ All API calls completed successfully');
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Module order updated successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('❌ Error updating module order:', error);
      setSnackbar({
        open: true,
        message: `Error updating order: ${error.message}`,
        severity: 'error'
      });
      // Restore original order on error
      setModules(originalModules);
    } finally {
      setReorderLoading(false);
    }
  };

  return (
    <Box sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: { xs: 2, sm: 3 },
        borderBottom: '1px solid',
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`
      }}>
        <Box>
          <Typography variant="h5" fontWeight={700}>
            Module Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Course: {courseName}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {!isReorderMode ? (
            <>
              <Button
                variant="outlined"
                startIcon={<ReorderIcon />}
                onClick={handleToggleReorderMode}
                disabled={loading || modules.length < 2}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Reorder Modules
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleToggleReorderMode}
                disabled={reorderLoading}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={reorderLoading ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleSaveOrder}
                disabled={reorderLoading}
                color="success"
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                {reorderLoading ? 'Saving...' : 'Save Order'}
              </Button>
            </>
          )}
          <IconButton 
            onClick={onClose}
            sx={{
              bgcolor: alpha(theme.palette.grey[500], 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.grey[500], 0.2)
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: { xs: 2, sm: 3 } }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={48} />
          </Box>
        ) : (
          <>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              mb: 3
            }}>
              <Typography variant="h6" fontWeight={600}>
                Total Modules: {modules.length}
              </Typography>
              {isReorderMode && (
                <Chip 
                  label="Reorder Mode Publish" 
                  color="primary" 
                  variant="filled"
                  sx={{ fontWeight: 600 }}
                />
              )}
            </Box>
            
            {modules.length === 0 ? (
              <Paper sx={{ 
                p: 4, 
                textAlign: 'center', 
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                border: '2px dashed',
                borderColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: 3
              }}>
                <Typography color="text.secondary" gutterBottom variant="h6">
                  No modules in this course
                </Typography>
                <Typography color="text.secondary" sx={{ mb: 3 }}>
                  Modules can be assigned to this course when creating or editing them
                </Typography>
              </Paper>
            ) : (
              <DndContext 
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext 
                  items={modules.map(module => module.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <List disablePadding>
                    {modules.map((module, index) => (
                      <SortableModuleItem
                        key={module.id}
                        module={module}
                        index={index}
                        onEdit={handleEditModule}
                        onDelete={(module) => {
                          setModuleToDelete(module);
                          setOpenDeleteDialog(true);
                        }}
                        loading={loading}
                        isReorderMode={isReorderMode}
                        theme={theme}
                      />
                    ))}
                  </List>
                </SortableContext>
              </DndContext>
            )}
          </>
        )}
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        aria-labelledby="delete-module-dialog"
      >
        <DialogTitle>
          Confirm Module Deletion
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Are you sure you want to delete the module "{moduleToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteModule}
            color="error" 
            variant="contained"
            disabled={loading}
          >
            Delete Module
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ModuleCourseManager;