import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Snackbar,
  Alert,
  Divider,
  Chip,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Close as CloseIcon,
  DragIndicator as DragIndicatorIcon,
  Reorder as ReorderIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useNavigate } from 'react-router-dom';
import adminModuleService from '../services/adminModuleService';
import StepTypeSelector from './StepTypeSelector';

// Sortable Step Item Component
function SortableStepItem({ step, index, stepType, onEdit, onDelete, loading, isReorderMode, theme }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: step.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const stepTypeLabel = stepType === '3d-model' ? '3D Model' : 
                       stepType === 'video' ? 'Video' : 
                       stepType === 'image' ? 'Image' : 'Unknown';

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <ListItem
      ref={setNodeRef}
      style={style}
      sx={{
        border: '2px solid',
        borderColor: isDragging ? theme.palette.primary.main : 'divider',
        borderRadius: 2,
        mb: 1,
        bgcolor: isDragging ? alpha(theme.palette.primary.main, 0.05) : 'background.paper',
        cursor: isReorderMode ? 'grab' : 'default',
        transition: 'all 0.2s ease',
        '&:hover': isReorderMode ? {
          borderColor: theme.palette.primary.main,
          boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
        } : {},
        ...(isReorderMode && {
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: '4px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            borderRadius: '2px 0 0 2px',
          }
        })
      }}
      {...(isReorderMode ? { ...attributes, ...listeners } : {})}
    >
      {isReorderMode && (
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mr: 2,
          color: 'primary.main'
        }}>
          <DragIndicatorIcon />
        </Box>
      )}
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label={`#${step.order || index + 1}`} 
              size="small" 
              color="primary" 
              sx={{ fontWeight: 600 }}
            />
            <Typography variant="subtitle1" fontWeight={600}>
              {step.title || 'Untitled Step'}
            </Typography>
            <Chip 
              label={stepTypeLabel} 
              size="small" 
              variant="outlined"
              color={stepType === '3d-model' ? 'primary' : 
                     stepType === 'video' ? 'error' : 
                     stepType === 'image' ? 'success' : 'default'}
            />
            {isReorderMode && (
              <Chip 
                label="Drag to reorder" 
                size="small" 
                variant="filled"
                color="info"
                sx={{ ml: 'auto', fontSize: '0.75rem' }}
              />
            )}
          </Box>
        }
        secondary={
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {step.content ? 
                (step.content.length > 100 ? 
                  step.content.substring(0, 100) + '...' : 
                  step.content
                ) : 'No content'
              }
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip 
                label={`Duration: ${step.duration || 30}s`} 
                size="small" 
                variant="outlined"
              />
              {stepType === '3d-model' && (
                <>
                  <Chip 
                    label={`Hotspots: ${step.hotspots?.length || 0}`} 
                    size="small" 
                    variant="outlined"
                  />
                </>
              )}
              {stepType === 'video' && (
                <Chip 
                  label={`Annotations: ${step.videoAnnotations?.length || 0}`} 
                  size="small" 
                  variant="outlined"
                />
              )}
              {stepType === 'image' && (
                <Chip 
                  label={`Annotations: ${step.imageAnnotations?.length || 0}`} 
                  size="small" 
                  variant="outlined"
                />
              )}
              <Chip 
                label={`Updated: ${formatDate(step.updatedAt)}`} 
                size="small" 
                variant="outlined"
              />
            </Box>
          </Box>
        }
      />
      
      {!isReorderMode && (
        <ListItemSecondaryAction>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => onEdit(step)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.2),
                }
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(step)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.error.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.error.main, 0.2),
                }
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </ListItemSecondaryAction>
      )}
    </ListItem>
  );
}

function StepsManager({ moduleId, moduleName, moduleData, onClose }) {
  const [steps, setSteps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedStep, setSelectedStep] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [stepToDelete, setStepToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [openStepTypeSelector, setOpenStepTypeSelector] = useState(false);
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [originalSteps, setOriginalSteps] = useState([]);
  const [reorderLoading, setReorderLoading] = useState(false);
  
  const navigate = useNavigate();
  const theme = useTheme();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load steps
  useEffect(() => {
    const loadSteps = async () => {
      try {
        setLoading(true);
        const stepsResponse = await adminModuleService.getSteps(moduleId);
        setSteps(stepsResponse || []);
      } catch (error) {
        console.error('Error loading steps:', error);
        setSnackbar({
          open: true,
          message: `Error loading steps: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    if (moduleId) {
      loadSteps();
    }
  }, [moduleId]);

  // Detect step type based on step data
  const detectStepType = (step) => {
    if (step.stepType) {
      return step.stepType;
    }
    
    // Fallback detection based on content
    if (step.videoUrl || step.videoFile) {
      return 'video';
    } else if (step.imageUrl || step.imageFile) {
      return 'image';
    } else if (step.hotspots || step.camera) {
      return '3d-model';
    }
    
    // Default to 3d-model for backward compatibility
    return '3d-model';
  };

  // Navigate to appropriate editor based on step type
  const navigateToEditor = (stepType, stepData = null) => {
    // Ensure we have full module data
    const fullModuleData = moduleData || { id: moduleId, title: moduleName };
    
    const navigationState = {
      moduleId: moduleId,
      moduleData: fullModuleData
    };

    if (stepData) {
      // Edit existing step - use edit files
      navigationState.stepId = stepData.id;
      navigationState.stepData = stepData;
      
      console.log('🚀 Navigating to edit step with full data:', {
        stepType,
        moduleData: fullModuleData,
        stepData: stepData
      });
      
      switch (stepType) {
        case '3d-model':
          navigate('/edit-step-editor', { state: navigationState });
          break;
        case 'video':
          navigate('/edit-video-step', { state: navigationState });
          break;
        case 'image':
          navigate('/edit-image-step', { state: navigationState });
          break;
        default:
          console.warn('Unknown step type for editing:', stepType);
      }
    } else {
      // Create new step - use create files
      console.log('🚀 Navigating to create step with full data:', {
        stepType,
        moduleData: fullModuleData
      });
      
      switch (stepType) {
        case '3d-model':
          navigate('/step-editor', { state: navigationState });
          break;
        case 'video':
          navigate('/video-step-editor', { state: navigationState });
          break;
        case 'image':
          navigate('/image-step-editor', { state: navigationState });
          break;
        default:
          console.warn('Unknown step type for creation:', stepType);
      }
    }
  };

  const handleCreateStep = () => {
    setOpenStepTypeSelector(true);
  };

  const handleSelectStepType = (stepType) => {
    setOpenStepTypeSelector(false);
    navigateToEditor(stepType);
    
    setSnackbar({
      open: true,
      message: `Opening ${stepType} step editor...`,
      severity: 'info'
    });
  };

  const handleEditStep = (step) => {
    const stepType = detectStepType(step);
    navigateToEditor(stepType, step);
    
    setSnackbar({
      open: true,
      message: `Opening ${stepType} step editor for editing...`,
      severity: 'info'
    });
  };

  const handleDeleteStep = async () => {
    if (!stepToDelete) return;

    try {
      setLoading(true);
      await adminModuleService.deleteStep(moduleId, stepToDelete.id);
      
      // Remove from local state
      setSteps(steps.filter(s => s.id !== stepToDelete.id));
      
      setSnackbar({
        open: true,
        message: 'Step deleted successfully!',
        severity: 'info'
      });
      
      setOpenDeleteDialog(false);
      setStepToDelete(null);
    } catch (error) {
      console.error('Error deleting step:', error);
      setSnackbar({
        open: true,
        message: `Error deleting step: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle drag end for reordering
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setSteps((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        const newOrder = arrayMove(items, oldIndex, newIndex);
        
        // Update order property for each step
        return newOrder.map((step, index) => ({
          ...step,
          order: index + 1
        }));
      });
    }
  };

  // Toggle reorder mode
  const handleToggleReorderMode = () => {
    if (!isReorderMode) {
      // Entering reorder mode - save original order
      setOriginalSteps([...steps]);
      setIsReorderMode(true);
      setSnackbar({
        open: true,
        message: 'Reorder mode enabled. Drag steps to change their order.',
        severity: 'info'
      });
    } else {
      // Exiting reorder mode - restore original order
      setSteps(originalSteps);
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Reorder mode cancelled. Order restored.',
        severity: 'info'
      });
    }
  };

  // Save new order
  const handleSaveOrder = async () => {
    try {
      console.log('🔄 Starting handleSaveOrder...');
      console.log('📋 Current steps:', steps.map(s => ({ id: s.id, title: s.title, order: s.order })));
      
      setReorderLoading(true);
      
      // Update order for each step - always call API to ensure sync
      const updatePromises = steps.map((step, index) => {
        const newOrder = index + 1;
        console.log(`🔄 Processing step ${step.id}: current order ${step.order} -> new order ${newOrder}`);
        
        // Always update to ensure order is synchronized
        return adminModuleService.updateStepOrder(moduleId, step.id, newOrder);
      });

      console.log('🚀 Calling API for all steps...');
      await Promise.all(updatePromises);
      
      console.log('✅ All API calls completed successfully');
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Step order updated successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('❌ Error updating step order:', error);
      setSnackbar({
        open: true,
        message: `Error updating order: ${error.message}`,
        severity: 'error'
      });
      // Restore original order on error
      setSteps(originalSteps);
    } finally {
      setReorderLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Box sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: { xs: 2, sm: 3 },
        borderBottom: '1px solid',
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`
      }}>
        <Box>
          <Typography variant="h5" fontWeight={700}>
            Steps Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Module: {moduleName}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {!isReorderMode ? (
            <>
              <Button
                variant="outlined"
                startIcon={<ReorderIcon />}
                onClick={handleToggleReorderMode}
                disabled={loading || steps.length < 2}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Reorder Steps
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateStep}
                disabled={loading}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Add Step
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleToggleReorderMode}
                disabled={reorderLoading}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={reorderLoading ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleSaveOrder}
                disabled={reorderLoading}
                color="success"
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                {reorderLoading ? 'Saving...' : 'Save Order'}
              </Button>
            </>
          )}
          <IconButton 
            onClick={onClose}
            sx={{
              bgcolor: alpha(theme.palette.grey[500], 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.grey[500], 0.2)
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: { xs: 2, sm: 3 } }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={48} />
          </Box>
        ) : (
          <>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              mb: 3
            }}>
              <Typography variant="h6" fontWeight={600}>
                Total Steps: {steps.length}
              </Typography>
              {isReorderMode && (
                <Chip 
                  label="Reorder Mode Active" 
                  color="primary" 
                  variant="filled"
                  sx={{ fontWeight: 600 }}
                />
              )}
            </Box>
            
            {steps.length === 0 ? (
              <Paper sx={{ 
                p: 4, 
                textAlign: 'center', 
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                border: '2px dashed',
                borderColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: 3
              }}>
                <Typography color="text.secondary" gutterBottom variant="h6">
                  No steps created yet
                </Typography>
                <Typography color="text.secondary" sx={{ mb: 3 }}>
                  Create your first step to get started with this module
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateStep}
                  size="large"
                  sx={{ 
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 600
                  }}
                >
                  Create First Step
                </Button>
              </Paper>
            ) : (
              <DndContext 
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext 
                  items={steps.map(step => step.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <List disablePadding>
                    {steps.map((step, index) => {
                      const stepType = detectStepType(step);
                      
                      return (
                        <SortableStepItem
                          key={step.id}
                          step={step}
                          index={index}
                          stepType={stepType}
                          onEdit={handleEditStep}
                          onDelete={(step) => {
                            setStepToDelete(step);
                            setOpenDeleteDialog(true);
                          }}
                          loading={loading}
                          isReorderMode={isReorderMode}
                          theme={theme}
                        />
                      );
                    })}
                  </List>
                </SortableContext>
              </DndContext>
            )}
          </>
        )}
      </Box>

      {/* Step Type Selector Dialog */}
      <StepTypeSelector
        open={openStepTypeSelector}
        onClose={() => setOpenStepTypeSelector(false)}
        onSelectType={handleSelectStepType}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        aria-labelledby="delete-step-dialog"
      >
        <DialogTitle>
          Confirm Step Deletion
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Are you sure you want to delete the step "{stepToDelete?.title}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteStep}
            color="error" 
            variant="contained"
            disabled={loading}
          >
            Delete Step
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default StepsManager;