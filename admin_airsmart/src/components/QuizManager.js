import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Snackbar,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Switch,
  useTheme,
  alpha
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  Quiz as QuizIcon,
  DragIndicator as DragIndicatorIcon,
  Reorder as ReorderIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import adminModuleService from '../services/adminModuleService';


// Sortable Quiz Item Component
function SortableQuizItem({ quiz, index, onEdit, onDelete, loading, isReorderMode, theme }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: quiz.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <ListItem
      ref={setNodeRef}
      style={style}
      sx={{
        border: '2px solid',
        borderColor: isDragging ? theme.palette.primary.main : 'divider',
        borderRadius: 2,
        mb: 1,
        bgcolor: isDragging ? alpha(theme.palette.primary.main, 0.05) : 'background.paper',
        cursor: isReorderMode ? 'grab' : 'default',
        transition: 'all 0.2s ease',
        '&:hover': isReorderMode ? {
          borderColor: theme.palette.primary.main,
          boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
        } : {},
        ...(isReorderMode && {
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: '4px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            borderRadius: '2px 0 0 2px',
          }
        })
      }}
      {...(isReorderMode ? { ...attributes, ...listeners } : {})}
    >
      {isReorderMode && (
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          mr: 2,
          color: 'primary.main'
        }}>
          <DragIndicatorIcon />
        </Box>
      )}
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label={`Q${quiz.order || index + 1}`} 
              size="small" 
              color="primary" 
              sx={{ fontWeight: 600 }}
            />
            <Chip 
              label={quiz.questionType === 'multiple' ? 'Multiple Choice' : 'Single Choice'} 
              size="small" 
              variant="outlined"
              color={quiz.questionType === 'multiple' ? 'secondary' : 'primary'}
            />
            {isReorderMode && (
              <Chip 
                label="Drag to reorder" 
                size="small" 
                variant="filled"
                color="info"
                sx={{ ml: 'auto', fontSize: '0.75rem' }}
              />
            )}
          </Box>
        }
        secondary={
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.primary" gutterBottom>
              {quiz.question}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip 
                label={`Options: ${quiz.options?.length || 0}`} 
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={`Correct: ${Array.isArray(quiz.correctAnswer) ? 
                  quiz.correctAnswer.length : 1} answer(s)`} 
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={`Updated: ${formatDate(quiz.updatedAt)}`} 
                size="small" 
                variant="outlined"
              />
            </Box>
            {quiz.explanation && (
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                Explanation: {quiz.explanation.substring(0, 50)}...
              </Typography>
            )}
          </Box>
        }
      />
      
      {!isReorderMode && (
        <ListItemSecondaryAction>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => onEdit(quiz)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.2),
                }
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(quiz)}
              disabled={loading}
              sx={{
                bgcolor: alpha(theme.palette.error.main, 0.1),
                '&:hover': {
                  bgcolor: alpha(theme.palette.error.main, 0.2),
                }
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </ListItemSecondaryAction>
      )}
    </ListItem>
  );
}

function QuizManager({ moduleId, moduleName, onClose }) {
  const [quizQuestions, setQuizQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openQuizDialog, setOpenQuizDialog] = useState(false);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [quizToDelete, setQuizToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [originalQuizzes, setOriginalQuizzes] = useState([]);
  const [reorderLoading, setReorderLoading] = useState(false);
  
  // Passing Score state
  const [passingScore, setPassingScore] = useState(70);
  const [originalPassingScore, setOriginalPassingScore] = useState(70);
  const [passingScoreChanged, setPassingScoreChanged] = useState(false);

  // Quiz Form Data
  const [quizForm, setQuizForm] = useState({
    question: '',
    options: ['', ''], // Start with 2 options minimum
    correctAnswer: 0,
    correctAnswers: [], // For multiple choice
    questionType: 'single', // 'single' or 'multiple'
    explanation: ''
  });

  const theme = useTheme();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load quiz questions and passing score
  useEffect(() => {
    const loadQuizData = async () => {
      try {
        setLoading(true);
        
        // Load quiz questions
        const quizResponse = await adminModuleService.getQuizQuestions(moduleId);
        setQuizQuestions(quizResponse || []);
        
        // Load module data to get current passing score
        const moduleData = await adminModuleService.getModule(moduleId);
        const currentPassingScore = moduleData?.passingScore || 70;
        setPassingScore(currentPassingScore);
        setOriginalPassingScore(currentPassingScore);
        
      } catch (error) {
        console.error('Error loading quiz data:', error);
        setSnackbar({
          open: true,
          message: `Error loading quiz data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    if (moduleId) {
      loadQuizData();
    }
  }, [moduleId]);

  const resetQuizForm = () => {
    setQuizForm({
      question: '',
      options: ['', ''], // Reset to 2 options minimum
      correctAnswer: 0,
      correctAnswers: [], // For multiple choice
      questionType: 'single', // 'single' or 'multiple'
      explanation: ''
    });
  };

  const handleCreateQuiz = () => {
    setSelectedQuiz(null);
    resetQuizForm();
    setOpenQuizDialog(true);
  };

  const handleEditQuiz = (quiz) => {
    setSelectedQuiz(quiz);
    
    // Handle both old and new quiz structures
    let questionType = 'single';
    let correctAnswer = 0;
    let correctAnswers = [];
    
    if (quiz.questionType) {
      // New structure
      questionType = quiz.questionType;
      if (questionType === 'multiple') {
        correctAnswers = Array.isArray(quiz.correctAnswer) ? quiz.correctAnswer : [];
      } else {
        correctAnswer = Array.isArray(quiz.correctAnswer) ? quiz.correctAnswer[0] : quiz.correctAnswer;
      }
    } else {
      // Old structure - assume single choice
      correctAnswer = quiz.correctAnswer || 0;
    }
    
    setQuizForm({
      question: quiz.question || '',
      options: Array.isArray(quiz.options) ? quiz.options : ['', '', '', ''],
      correctAnswer: correctAnswer,
      correctAnswers: correctAnswers,
      questionType: questionType,
      explanation: quiz.explanation || ''
    });
    setOpenQuizDialog(true);
  };

  const handleSaveQuiz = async () => {
    // Validate form data
    if (!quizForm.question.trim()) {
      setSnackbar({
        open: true,
        message: 'Question is required',
        severity: 'error'
      });
      return;
    }

    const validOptions = quizForm.options.filter(opt => opt.trim() !== '');
    if (validOptions.length < 2) {
      setSnackbar({
        open: true,
        message: 'At least 2 options are required',
        severity: 'error'
      });
      return;
    }

    // Prepare quiz data based on question type
    let quizData;
    
    if (quizForm.questionType === 'multiple') {
      // Multiple choice - correctAnswer is array of indices
      const correctAnswers = quizForm.correctAnswers || [];
      if (correctAnswers.length === 0) {
        setSnackbar({
          open: true,
          message: 'At least one correct answer must be selected',
          severity: 'error'
        });
        return;
      }

      quizData = {
        question: quizForm.question,
        options: validOptions,
        correctAnswer: correctAnswers, // Array of indices
        questionType: 'multiple',
        explanation: quizForm.explanation,
        order: selectedQuiz ? selectedQuiz.order : (quizQuestions.length + 1),
      };
    } else {
      // Single choice - correctAnswer is single index
      if (quizForm.correctAnswer === undefined || quizForm.correctAnswer === null) {
        setSnackbar({
          open: true,
          message: 'A correct answer must be selected',
          severity: 'error'
        });
        return;
      }

      quizData = {
        question: quizForm.question,
        options: validOptions,
        correctAnswer: [quizForm.correctAnswer], // Convert to array for consistency
        questionType: 'single',
        explanation: quizForm.explanation,
        order: selectedQuiz ? selectedQuiz.order : (quizQuestions.length + 1),
      };
    }
    
    try {
      setLoading(true);
      
      if (selectedQuiz) {
        // Update existing quiz
        const updatedQuiz = await adminModuleService.updateQuizQuestion(
          moduleId,
          selectedQuiz.id,
          quizData
        );
        
        // Update local state
        setQuizQuestions(quizQuestions.map(quiz => 
          quiz.id === selectedQuiz.id ? { ...quiz, ...updatedQuiz } : quiz
        ));
        
        setSnackbar({
          open: true,
          message: 'Quiz question updated successfully!',
          severity: 'success'
        });
      } else {
        // Create new quiz
        const newQuiz = await adminModuleService.createQuizQuestion(moduleId, quizData);
        
        // Add to local state
        setQuizQuestions([...quizQuestions, newQuiz]);
        
        setSnackbar({
          open: true,
          message: 'Quiz question created successfully!',
          severity: 'success'
        });
      }
      
      setOpenQuizDialog(false);
      setSelectedQuiz(null);
      resetQuizForm();
      
    } catch (error) {
      console.error('Error saving quiz:', error);
      setSnackbar({
        open: true,
        message: `Error saving quiz: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuiz = async () => {
    if (!quizToDelete) return;
    
    try {
      setLoading(true);
      
      await adminModuleService.deleteQuizQuestion(moduleId, quizToDelete.id);
      
      // Remove from local state
      setQuizQuestions(quizQuestions.filter(q => q.id !== quizToDelete.id));
      
      setSnackbar({
        open: true,
        message: 'Quiz question deleted successfully!',
        severity: 'info'
      });
      
      setOpenDeleteDialog(false);
      setQuizToDelete(null);
    } catch (error) {
      console.error('Error deleting quiz:', error);
      setSnackbar({
        open: true,
        message: `Error deleting quiz: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle passing score change
  const handlePassingScoreChange = (newScore) => {
    setPassingScore(newScore);
    setPassingScoreChanged(newScore !== originalPassingScore);
  };

  // Save passing score
  const handleSavePassingScore = async () => {
    try {
      setLoading(true);
      
      // Update module with new passing score
      await adminModuleService.updateModule(moduleId, {
        passingScore: passingScore
      });
      
      setOriginalPassingScore(passingScore);
      setPassingScoreChanged(false);
      
      setSnackbar({
        open: true,
        message: 'Passing score updated successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error updating passing score:', error);
      setSnackbar({
        open: true,
        message: `Error updating passing score: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Reset passing score
  const handleResetPassingScore = () => {
    setPassingScore(originalPassingScore);
    setPassingScoreChanged(false);
  };

  // Helper functions for managing options
  const addOption = () => {
    setQuizForm(prev => ({
      ...prev,
      options: [...prev.options, '']
    }));
  };

  const removeOption = (index) => {
    if (quizForm.options.length <= 2) {
      setSnackbar({
        open: true,
        message: 'Minimum 2 options required',
        severity: 'warning'
      });
      return;
    }

    setQuizForm(prev => {
      const newOptions = prev.options.filter((_, i) => i !== index);
      
      // Adjust correct answers when removing options
      let newCorrectAnswer = prev.correctAnswer;
      let newCorrectAnswers = [...prev.correctAnswers];

      if (prev.questionType === 'single') {
        // For single choice, adjust if correct answer index is affected
        if (prev.correctAnswer === index) {
          newCorrectAnswer = 0; // Reset to first option
        } else if (prev.correctAnswer > index) {
          newCorrectAnswer = prev.correctAnswer - 1; // Shift down
        }
      } else {
        // For multiple choice, remove index from correct answers and adjust higher indices
        newCorrectAnswers = prev.correctAnswers
          .filter(answerIndex => answerIndex !== index) // Remove if this option was correct
          .map(answerIndex => answerIndex > index ? answerIndex - 1 : answerIndex); // Shift down higher indices
      }

      return {
        ...prev,
        options: newOptions,
        correctAnswer: newCorrectAnswer,
        correctAnswers: newCorrectAnswers
      };
    });
  };

  // Reorder functionality - matching StepsManager exactly
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setQuizQuestions((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        const newOrder = arrayMove(items, oldIndex, newIndex);
        
        // Update order property for each quiz question
        return newOrder.map((quiz, index) => ({
          ...quiz,
          order: index + 1
        }));
      });
    }
  };

  // Toggle reorder mode - matching StepsManager exactly
  const handleToggleReorderMode = () => {
    if (!isReorderMode) {
      // Entering reorder mode - save original order
      setOriginalQuizzes([...quizQuestions]);
      setIsReorderMode(true);
      setSnackbar({
        open: true,
        message: 'Reorder mode enabled. Drag quiz questions to change their order.',
        severity: 'info'
      });
    } else {
      // Exiting reorder mode - restore original order
      setQuizQuestions(originalQuizzes);
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Reorder mode cancelled. Order restored.',
        severity: 'info'
      });
    }
  };

  // Save new order - matching StepsManager exactly
  const handleSaveOrder = async () => {
    try {
      console.log('🔄 Starting handleSaveOrder for quiz questions...');
      console.log('📋 Current quiz questions:', quizQuestions.map(q => ({ id: q.id, question: q.question, order: q.order })));
      
      setReorderLoading(true);
      
      // Update order for each quiz question - always call API to ensure sync
      const updatePromises = quizQuestions.map((quiz, index) => {
        const newOrder = index + 1;
        console.log(`🔄 Processing quiz ${quiz.id}: current order ${quiz.order} -> new order ${newOrder}`);
        
        // Always update to ensure order is synchronized
        return adminModuleService.updateQuizOrder(moduleId, quiz.id, newOrder);
      });

      console.log('🚀 Calling API for all quiz questions...');
      await Promise.all(updatePromises);
      
      console.log('✅ All API calls completed successfully');
      setIsReorderMode(false);
      setSnackbar({
        open: true,
        message: 'Quiz order updated successfully!',
        severity: 'success'
      });
    } catch (error) {
      console.error('❌ Error updating quiz order:', error);
      setSnackbar({
        open: true,
        message: `Error updating order: ${error.message}`,
        severity: 'error'
      });
      // Restore original order on error
      setQuizQuestions(originalQuizzes);
    } finally {
      setReorderLoading(false);
    }
  };

  return (
    <Box sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: { xs: 2, sm: 3 },
        borderBottom: '1px solid',
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`
      }}>
        <Box>
          <Typography variant="h5" fontWeight={700}>
            Quiz Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Module: {moduleName}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {!isReorderMode ? (
            <>
              <Button
                variant="outlined"
                startIcon={<ReorderIcon />}
                onClick={handleToggleReorderMode}
                disabled={loading || quizQuestions.length < 2}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Reorder Quiz
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateQuiz}
                disabled={loading}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Add Question
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleToggleReorderMode}
                disabled={reorderLoading}
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={reorderLoading ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleSaveOrder}
                disabled={reorderLoading}
                color="success"
                size="medium"
                sx={{ 
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                {reorderLoading ? 'Saving...' : 'Save Order'}
              </Button>
            </>
          )}
          <IconButton 
            onClick={onClose}
            sx={{
              bgcolor: alpha(theme.palette.grey[500], 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.grey[500], 0.2)
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: { xs: 2, sm: 3 } }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={48} />
          </Box>
        ) : (
          <>
            {/* Passing Score Section - Only show if there are quiz questions */}
            {quizQuestions.length > 0 && (
              <Paper sx={{ 
                p: 3, 
                mb: 3,
                bgcolor: alpha(theme.palette.success.main, 0.05),
                border: '1px solid',
                borderColor: alpha(theme.palette.success.main, 0.2),
                borderRadius: 2
              }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between',
                  mb: 2
                }}>
                  <Typography variant="h6" fontWeight={600} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    🎯 Quiz Passing Score
                  </Typography>
                  {passingScoreChanged && (
                    <Chip 
                      label="Unsaved Changes" 
                      color="warning" 
                      size="small"
                      variant="filled"
                    />
                  )}
                </Box>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Set the minimum percentage score required for students to pass this quiz
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                  <TextField
                    label="Passing Score (%)"
                    type="number"
                    value={passingScore}
                    onChange={(e) => {
                      const value = Math.max(0, Math.min(100, parseInt(e.target.value) || 0));
                      handlePassingScoreChange(value);
                    }}
                    inputProps={{ min: 0, max: 100 }}
                    sx={{ width: 150 }}
                    size="small"
                    disabled={loading}
                  />
                  
                  {passingScoreChanged && (
                    <>
                      <Button
                        variant="contained"
                        color="success"
                        onClick={handleSavePassingScore}
                        disabled={loading}
                        size="small"
                        startIcon={<SaveIcon />}
                      >
                        Save
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={handleResetPassingScore}
                        disabled={loading}
                        size="small"
                        startIcon={<CancelIcon />}
                      >
                        Reset
                      </Button>
                    </>
                  )}
                  
                  <Typography variant="body2" color="text.secondary">
                    Current: {passingScore}% • Students need to score at least {passingScore}% to pass
                  </Typography>
                </Box>
              </Paper>
            )}

            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              mb: 3
            }}>
              <Typography variant="h6" fontWeight={600}>
                Quiz Questions ({quizQuestions.length})
              </Typography>
              {isReorderMode && (
                <Chip 
                  label="Reorder Mode Publish" 
                  color="primary" 
                  variant="filled"
                  sx={{ fontWeight: 600 }}
                />
              )}
            </Box>
            
            {quizQuestions.length === 0 ? (
              <Paper sx={{ 
                p: 4, 
                textAlign: 'center', 
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                border: '2px dashed',
                borderColor: alpha(theme.palette.primary.main, 0.2),
                borderRadius: 3
              }}>
                <Typography color="text.secondary" gutterBottom variant="h6">
                  No quiz questions created yet
                </Typography>
                <Typography color="text.secondary" sx={{ mb: 3 }}>
                  Create your first quiz question to get started with this module
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateQuiz}
                  size="large"
                  sx={{ 
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 600
                  }}
                >
                  Create First Question
                </Button>
              </Paper>
            ) : (
              <DndContext 
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext 
                  items={quizQuestions.map(quiz => quiz.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <List disablePadding>
                    {quizQuestions.map((quiz, index) => (
                      <SortableQuizItem
                        key={quiz.id}
                        quiz={quiz}
                        index={index}
                        onEdit={handleEditQuiz}
                        onDelete={(quiz) => {
                          setQuizToDelete(quiz);
                          setOpenDeleteDialog(true);
                        }}
                        loading={loading}
                        isReorderMode={isReorderMode}
                        theme={theme}
                      />
                    ))}
                  </List>
                </SortableContext>
              </DndContext>
            )}
          </>
        )}
      </Box>

      {/* Quiz Dialog */}
      <Dialog open={openQuizDialog} onClose={() => setOpenQuizDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedQuiz ? 'Edit Quiz Question' : 'Create New Quiz Question'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Question Type Selection */}
            <FormControl fullWidth>
              <InputLabel>Question Type</InputLabel>
              <Select
                value={quizForm.questionType}
                label="Question Type"
                onChange={(e) => {
                  const newType = e.target.value;
                  setQuizForm({
                    ...quizForm,
                    questionType: newType,
                    correctAnswer: newType === 'single' ? 0 : undefined,
                    correctAnswers: newType === 'multiple' ? [] : undefined
                  });
                }}
              >
                <MenuItem value="single">Single Choice (Radio buttons)</MenuItem>
                <MenuItem value="multiple">Multiple Choice (Checkboxes)</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Question"
              value={quizForm.question}
              onChange={(e) => setQuizForm({ ...quizForm, question: e.target.value })}
              multiline
              rows={2}
              fullWidth
              required
            />

            <Typography variant="subtitle2">
              Answer Options {quizForm.questionType === 'multiple' && '(Select all correct answers)'}:
            </Typography>
            
            {quizForm.options.map((option, index) => (
              <Box key={index} sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <TextField
                  label={`Option ${index + 1}`}
                  value={option}
                  onChange={(e) => {
                    const newOptions = [...quizForm.options];
                    newOptions[index] = e.target.value;
                    setQuizForm({ ...quizForm, options: newOptions });
                  }}
                  fullWidth
                />
                
                {/* Single Choice - Radio Button Logic */}
                {quizForm.questionType === 'single' && (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={quizForm.correctAnswer === index}
                        onChange={() => setQuizForm({ ...quizForm, correctAnswer: index })}
                        color="success"
                      />
                    }
                    label="Correct"
                  />
                )}

                {/* Multiple Choice - Checkbox Logic */}
                {quizForm.questionType === 'multiple' && (
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={quizForm.correctAnswers.includes(index)}
                        onChange={(e) => {
                          const newCorrectAnswers = [...quizForm.correctAnswers];
                          if (e.target.checked) {
                            newCorrectAnswers.push(index);
                          } else {
                            const removeIndex = newCorrectAnswers.indexOf(index);
                            if (removeIndex > -1) {
                              newCorrectAnswers.splice(removeIndex, 1);
                            }
                          }
                          setQuizForm({ ...quizForm, correctAnswers: newCorrectAnswers });
                        }}
                        color="success"
                      />
                    }
                    label="Correct"
                  />
                )}

                {/* Remove Option Button */}
                <IconButton
                  color="error"
                  onClick={() => removeOption(index)}
                  disabled={quizForm.options.length <= 2}
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            ))}

            {/* Add Option Button */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={addOption}
                disabled={quizForm.options.length >= 10} // Max 10 options
                size="small"
              >
                Add Option ({quizForm.options.length}/10)
              </Button>
            </Box>

            <TextField
              label="Explanation"
              value={quizForm.explanation}
              onChange={(e) => setQuizForm({ ...quizForm, explanation: e.target.value })}
              multiline
              rows={2}
              fullWidth
              placeholder="Explain why this is the correct answer(s)..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenQuizDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveQuiz}
            variant="contained"
            disabled={!quizForm.question || quizForm.options.some(opt => !opt.trim())}
          >
            {selectedQuiz ? 'Update Question' : 'Create Question'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        aria-labelledby="delete-quiz-dialog"
      >
        <DialogTitle>
          Confirm Quiz Deletion
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Are you sure you want to delete this quiz question? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteQuiz}
            color="error" 
            variant="contained"
            disabled={loading}
          >
            Delete Question
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default QuizManager;