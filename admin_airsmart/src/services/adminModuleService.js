import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  async (config) => {
    console.log(`🔄 Admin API Call: ${config.method?.toUpperCase()} ${config.url}`);

    // Get Firebase auth token if available
    // You can add auth logic here later
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ Admin API Response: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response.data; // Return only data part
  },
  (error) => {
    console.error('❌ Admin API Error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      console.warn('Admin authentication error');
    }
    
    return Promise.reject(error);
  }
);

class AdminModuleService {
  // ========================================
  // MODULE CRUD OPERATIONS
  // ========================================

  /**
   * Create a new module
   * @param {Object} moduleData - Module data
   * @returns {Promise<Object>} Created module
   */
  async createModule(moduleData) {
    try {
      const response = await api.post('/admin/modules', moduleData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create module');
    }
  }

  /**
   * Create a new module with 3D model upload
   * @param {FormData} formData - Form data containing module data and 3D model file
   * @returns {Promise<Object>} Created module with uploaded model URL
   */
  async createModuleWith3DModel(formData) {
    try {
      console.log('🔄 Creating module with 3D model...');
      const response = await api.post('/admin/modules/with-3d-model', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds timeout for large files
      });
      console.log('✅ Module with 3D model created:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating module with 3D model:', error);
      throw new Error(error.response?.data?.message || 'Failed to create module with 3D model');
    }
  }

  /**
   * Get all modules with admin metadata
   * @returns {Promise<Array>} Array of modules
   */
  async getModules() {
    try {
      const response = await api.get('/admin/modules');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get modules');
    }
  }

  /**
   * Get single module by ID
   * @param {string} id - Module ID
   * @returns {Promise<Object>} Module data
   */
  async getModule(id) {
    try {
      const response = await api.get(`/admin/modules/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get module');
    }
  }

  /**
   * Update a module
   * @param {string} id - Module ID
   * @param {Object} moduleData - Updated module data
   * @returns {Promise<Object>} Updated module
   */
  async updateModule(id, moduleData) {
    try {
      const response = await api.patch(`/admin/modules/${id}`, moduleData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update module');
    }
  }

  /**
   * Delete a module
   * @param {string} id - Module ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteModule(id) {
    try {
      const response = await api.delete(`/admin/modules/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete module');
    }
  }

  /**
   * Add 3D model to existing module
   * @param {string} moduleId - Module ID
   * @param {FormData} formData - Form data containing 3D model file
   * @returns {Promise<Object>} Updated module with 3D model URL
   */
  async add3DModelToModule(moduleId, formData) {
    try {
      console.log('🔄 Adding 3D model to existing module...');
      const response = await api.patch(`/admin/modules/${moduleId}/add-3d-model`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds timeout for large files
      });
      console.log('✅ 3D model added to module:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error adding 3D model to module:', error);
      throw new Error(error.response?.data?.message || 'Failed to add 3D model to module');
    }
  }

  // ========================================
  // CATEGORY CRUD OPERATIONS
  // ========================================

  /**
   * Create a new course
   * @param {Object} courseData - Course data
   * @returns {Promise<Object>} Created course
   */
  async createCourse(courseData) {
    try {
      const response = await api.post('/admin/courses', courseData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create course');
    }
  }

  /**
   * Get all courses with admin metadata
   * @returns {Promise<Array>} Array of courses
   */
  async getCourses() {
    try {
      const response = await api.get('/admin/courses');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get courses');
    }
  }

  /**
   * Get single course by ID
   * @param {string} id - Course ID
   * @returns {Promise<Object>} Course data
   */
  async getCourse(id) {
    try {
      const response = await api.get(`/admin/courses/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get course');
    }
  }

  /**
   * Update a course
   * @param {string} id - Course ID
   * @param {Object} courseData - Updated course data
   * @returns {Promise<Object>} Updated course
   */
  async updateCourse(id, courseData) {
    try {
      const response = await api.patch(`/admin/courses/${id}`, courseData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update course');
    }
  }

  /**
   * Delete a course
   * @param {string} id - Course ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteCourse(id) {
    try {
      const response = await api.delete(`/admin/courses/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete course');
    }
  }

  /**
   * Get user locked courses
   * @param {string} uid - User UID
   * @returns {Promise<Object>} User locked courses
   */
  async getUserLockedCourses(uid) {
    try {
      const response = await api.get(`/users/${uid}/coming-soon-courses`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get user Coming Soon courses');
    }
  }

  /**
   * Lock course for user
   * @param {string} uid - User UID
   * @param {string} courseId - Course ID to lock
   * @returns {Promise<Object>} Success response
   */
  async lockCourseForUser(uid, courseId) {
    try {
      const response = await api.post(`/users/${uid}/lock-course`, { courseId });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to lock course for user');
    }
  }

  /**
   * Unlock course for user
   * @param {string} uid - User UID
   * @param {string} courseId - Course ID to unlock
   * @returns {Promise<Object>} Success response
   */
  async unlockCourseForUser(uid, courseId) {
    try {
      const response = await api.delete(`/users/${uid}/unlock-course/${courseId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to unlock course for user');
    }
  }

  /**
   * Update locked courses for a user
   * @param {string} uid - User ID
   * @param {Array<string>} lockedCourses - Array of course IDs
   * @returns {Promise<Object>} Updated locked courses
   */
  async updateUserLockedCourses(uid, lockedCourses) {
    try {
      const response = await api.patch(`/users/${uid}/coming-soon-courses`, { lockedCourses });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update Coming Soon courses');
    }
  }

  // ========================================
  // STEP CRUD OPERATIONS
  // ========================================

  /**
   * Create a new step for a module
   * @param {string} moduleId - Module ID
   * @param {Object} stepData - Step data
   * @returns {Promise<Object>} Created step
   */
  async createStep(moduleId, stepData) {
    try {
      const response = await api.post(`/admin/modules/${moduleId}/steps`, stepData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create step');
    }
  }

  /**
   * Create a new step with image upload
   * @param {string} moduleId - Module ID
   * @param {FormData} formData - Form data containing step data and image file
   * @returns {Promise<Object>} Created step with uploaded image URL
   */
  async createStepWithImage(moduleId, formData) {
    try {
      console.log('🔄 Creating step with image...');
      const response = await api.post(`/admin/modules/${moduleId}/steps/with-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds timeout for large files
      });
      console.log('✅ Step with image created:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating step with image:', error);
      throw new Error(error.response?.data?.message || 'Failed to create step with image');
    }
  }

  /**
   * Create a new step with video upload
   * @param {string} moduleId - Module ID
   * @param {FormData} formData - Form data containing step data and video file
   * @returns {Promise<Object>} Created step with uploaded video URL
   */
  async createStepWithVideo(moduleId, formData) {
    try {
      console.log('🔄 Creating step with video...');
      const response = await api.post(`/admin/modules/${moduleId}/steps/with-video`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 minutes timeout for large video files
      });
      console.log('✅ Step with video created:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating step with video:', error);
      throw new Error(error.response?.data?.message || 'Failed to create step with video');
    }
  }

  /**
   * Get all steps for a module
   * @param {string} moduleId - Module ID
   * @returns {Promise<Array>} Array of steps
   */
  async getSteps(moduleId) {
    try {
      const response = await api.get(`/admin/modules/${moduleId}/steps`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get steps');
    }
  }

  /**
   * Get single step by ID
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @returns {Promise<Object>} Step data
   */
  async getStep(moduleId, stepId) {
    try {
      const response = await api.get(`/admin/modules/${moduleId}/steps/${stepId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get step');
    }
  }

  /**
   * Update a step
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @param {Object} stepData - Updated step data
   * @returns {Promise<Object>} Updated step
   */
  async updateStep(moduleId, stepId, stepData) {
    try {
      const response = await api.patch(`/admin/modules/${moduleId}/steps/${stepId}`, stepData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update step');
    }
  }

  /**
   * Update a step with image upload
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @param {FormData} formData - Form data containing step data and image file
   * @returns {Promise<Object>} Updated step with uploaded image URL
   */
  async updateStepWithImage(moduleId, stepId, formData) {
    try {
      console.log('🔄 Updating step with image...');
      const response = await api.patch(`/admin/modules/${moduleId}/steps/${stepId}/with-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds timeout for large files
      });
      console.log('✅ Step with image updated:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating step with image:', error);
      throw new Error(error.response?.data?.message || 'Failed to update step with image');
    }
  }

  /**
   * Update a step with video upload
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @param {FormData} formData - Form data containing step data and video file
   * @returns {Promise<Object>} Updated step with uploaded video URL
   */
  async updateStepWithVideo(moduleId, stepId, formData) {
    try {
      console.log('🔄 Updating step with video...');
      const response = await api.patch(`/admin/modules/${moduleId}/steps/${stepId}/with-video`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 minutes timeout for large video files
      });
      console.log('✅ Step with video updated:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating step with video:', error);
      throw new Error(error.response?.data?.message || 'Failed to update step with video');
    }
  }

  /**
   * Update step order
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @param {number} newOrder - New order position
   * @returns {Promise<Object>} Updated step
   */
  async updateStepOrder(moduleId, stepId, newOrder) {
    try {
      console.log(`🔄 Updating step order: ${stepId} to ${newOrder} in module: ${moduleId}`);
      const response = await api.patch(`/admin/modules/${moduleId}/steps/${stepId}/order`, {
        order: newOrder
      });
      console.log('✅ Step order updated successfully:', response);
      return response;
    } catch (error) {
      console.error('❌ Error updating step order:', error);
      throw new Error(error.response?.data?.message || 'Failed to update step order');
    }
  }

  /**
   * Delete a step
   * @param {string} moduleId - Module ID
   * @param {string} stepId - Step ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteStep(moduleId, stepId) {
    try {
      const response = await api.delete(`/admin/modules/${moduleId}/steps/${stepId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete step');
    }
  }

  // ========================================
  // QUIZ CRUD OPERATIONS
  // ========================================

  /**
   * Create a new quiz question for a module
   * @param {string} moduleId - Module ID
   * @param {Object} quizData - Quiz question data
   * @returns {Promise<Object>} Created quiz question
   */
  async createQuizQuestion(moduleId, quizData) {
    try {
      const response = await api.post(`/admin/modules/${moduleId}/quiz`, quizData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create quiz question');
    }
  }

  /**
   * Get all quiz questions for a module
   * @param {string} moduleId - Module ID
   * @returns {Promise<Array>} Array of quiz questions
   */
  async getQuizQuestions(moduleId) {
    try {
      const response = await api.get(`/admin/modules/${moduleId}/quiz`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get quiz questions');
    }
  }

  /**
   * Update a quiz question
   * @param {string} moduleId - Module ID
   * @param {string} quizId - Quiz question ID
   * @param {Object} quizData - Updated quiz data
   * @returns {Promise<Object>} Updated quiz question
   */
  async updateQuizQuestion(moduleId, quizId, quizData) {
    try {
      const response = await api.patch(`/admin/modules/${moduleId}/quiz/${quizId}`, quizData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update quiz question');
    }
  }

  /**
   * Delete a quiz question
   * @param {string} moduleId - Module ID
   * @param {string} quizId - Quiz question ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteQuizQuestion(moduleId, quizId) {
    try {
      const response = await api.delete(`/admin/modules/${moduleId}/quiz/${quizId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete quiz question');
    }
  }

  /**
   * Update quiz question order
   * @param {string} moduleId - Module ID
   * @param {string} quizId - Quiz question ID
   * @param {number} newOrder - New order position
   * @returns {Promise<Object>} Updated quiz question
   */
  async updateQuizOrder(moduleId, quizId, newOrder) {
    try {
      console.log(`🔄 Updating quiz order: ${quizId} to ${newOrder} in module: ${moduleId}`);
      const response = await api.patch(`/admin/modules/${moduleId}/quiz/${quizId}/order`, {
        order: newOrder
      });
      console.log('✅ Quiz order updated successfully:', response);
      return response;
    } catch (error) {
      console.error('❌ Error updating quiz order:', error);
      throw new Error(error.response?.data?.message || 'Failed to update quiz order');
    }
  }

  // ========================================
  // UTILITY METHODS
  // ========================================

  /**
   * Validate module data before sending to API
   * @param {Object} moduleData - Module data to validate
   * @returns {Object} Validation result
   */
  validateModuleData(moduleData) {
    const errors = [];

    if (!moduleData.name || moduleData.name.trim().length === 0) {
      errors.push('Module name is required');
    }

    if (moduleData.name && moduleData.name.length > 100) {
      errors.push('Module name must be less than 100 characters');
    }

    if (!moduleData.description || moduleData.description.trim().length === 0) {
      errors.push('Module description is required');
    }

    if (moduleData.description && moduleData.description.length > 1000) {
      errors.push('Module description must be less than 1000 characters');
    }

    if (moduleData.modelUrl && !this.isValidUrl(moduleData.modelUrl)) {
      errors.push('Model URL must be a valid URL');
    }

    if (moduleData.thumbnail && !this.isValidUrl(moduleData.thumbnail)) {
      errors.push('Thumbnail must be a valid URL');
    }

    if (moduleData.passingScore && (moduleData.passingScore < 0 || moduleData.passingScore > 100)) {
      errors.push('Passing score must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate course data before sending to API
   * @param {Object} courseData - Course data to validate
   * @returns {Object} Validation result
   */
  validateCourseData(courseData) {
    const errors = [];

    if (!courseData.name || courseData.name.trim().length === 0) {
      errors.push('Course name is required');
    }

    if (courseData.name && courseData.name.length > 100) {
      errors.push('Course name must be less than 100 characters');
    }

    if (!courseData.description || courseData.description.trim().length === 0) {
      errors.push('Course description is required');
    }

    if (courseData.description && courseData.description.length > 1000) {
      errors.push('Course description must be less than 1000 characters');
    }

    if (courseData.thumbnail && !this.isValidUrl(courseData.thumbnail)) {
      errors.push('Thumbnail must be a valid URL');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate step data before sending to API
   * @param {Object} stepData - Step data to validate
   * @returns {Object} Validation result
   */
  validateStepData(stepData) {
    const errors = [];

    if (!stepData.title || stepData.title.trim().length === 0) {
      errors.push('Step title is required');
    }

    if (stepData.title && stepData.title.length > 200) {
      errors.push('Step title must be less than 200 characters');
    }

    if (!stepData.content || stepData.content.trim().length === 0) {
      errors.push('Step content is required');
    }

    if (stepData.content && stepData.content.length > 2000) {
      errors.push('Step content must be less than 2000 characters');
    }

    if (stepData.order && stepData.order < 1) {
      errors.push('Step order must be at least 1');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate quiz question data before sending to API
   * @param {Object} quizData - Quiz data to validate
   * @returns {Object} Validation result
   */
  validateQuizData(quizData) {
    const errors = [];

    if (!quizData.question || quizData.question.trim().length === 0) {
      errors.push('Quiz question is required');
    }

    if (quizData.question && quizData.question.length > 1000) {
      errors.push('Quiz question must be less than 1000 characters');
    }

    if (!quizData.options || !Array.isArray(quizData.options) || quizData.options.length < 2) {
      errors.push('At least 2 answer options are required');
    }

    if (quizData.options && quizData.options.some(opt => !opt.trim())) {
      errors.push('All answer options must have text');
    }

    if (quizData.correctAnswer !== undefined && (quizData.correctAnswer < 0 || quizData.correctAnswer >= quizData.options.length)) {
      errors.push('Correct answer index is invalid');
    }

    if (quizData.points && quizData.points < 1) {
      errors.push('Points must be at least 1');
    }

    if (quizData.timeLimit && quizData.timeLimit < 10) {
      errors.push('Time limit must be at least 10 seconds');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if a string is a valid URL
   * @param {string} string - String to validate
   * @returns {boolean} Is valid URL
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  /**
   * Format module data for display
   * @param {Object} module - Raw module data
   * @returns {Object} Formatted module data
   */
  formatModuleForDisplay(module) {
    return {
      ...module,
      createdAtFormatted: module.createdAt ? new Date(module.createdAt).toLocaleDateString() : 'N/A',
      lastUpdatedFormatted: module.lastUpdated ? new Date(module.lastUpdated).toLocaleDateString() : 'N/A',
      tagsDisplay: Array.isArray(module.tags) ? module.tags.join(', ') : '',
      statusDisplay: module.isLocked ? 'Coming Soon' : 'Available',
      courseDisplay: module.course || 'Standalone'
    };
  }

  /**
   * Format course data for display
   * @param {Object} course - Raw course data
   * @returns {Object} Formatted course data
   */
  formatCourseForDisplay(course) {
    return {
      ...course,
      createdAtFormatted: course.createdAt ? new Date(course.createdAt).toLocaleDateString() : 'N/A',
      lastUpdatedFormatted: course.lastUpdated ? new Date(course.lastUpdated).toLocaleDateString() : 'N/A',
      tagsDisplay: Array.isArray(course.tags) ? course.tags.join(', ') : '',
      statusDisplay: course.isActive ? 'Publish' : 'Inactive'
    };
  }

  /**
   * Format step data for display
   * @param {Object} step - Raw step data
   * @returns {Object} Formatted step data
   */
  formatStepForDisplay(step) {
    return {
      ...step,
      createdAtFormatted: step.createdAt ? new Date(step.createdAt).toLocaleDateString() : 'N/A',
      lastUpdatedFormatted: step.updatedAt ? new Date(step.updatedAt).toLocaleDateString() : 'N/A',
      hotspotsCount: step.hotspots ? step.hotspots.length : 0,
      actionsCount: step.actions ? step.actions.length : 0,
      durationFormatted: step.duration ? `${step.duration}s` : 'No limit'
    };
  }

  /**
   * Format quiz data for display
   * @param {Object} quiz - Raw quiz data
   * @returns {Object} Formatted quiz data
   */
  formatQuizForDisplay(quiz) {
    return {
      ...quiz,
      createdAtFormatted: quiz.createdAt ? new Date(quiz.createdAt).toLocaleDateString() : 'N/A',
      lastUpdatedFormatted: quiz.updatedAt ? new Date(quiz.updatedAt).toLocaleDateString() : 'N/A',
      optionsCount: quiz.options ? quiz.options.length : 0,
      timeLimitFormatted: quiz.timeLimit ? `${quiz.timeLimit}s` : 'No limit',
      difficultyDisplay: quiz.difficulty || 'Medium'
    };
  }

  /**
   * Upload thumbnail image
   * @param {FormData} formData - Form data containing thumbnail file
   * @param {Function} onProgress - Progress callback function
   * @returns {Promise<Object>} Upload result with URL
   */
  async uploadThumbnail(formData, onProgress) {
    try {
      console.log('🔄 Uploading thumbnail...');
      const response = await api.post('/admin/modules/upload-thumbnail', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds timeout for large files
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            onProgress(progressEvent);
          }
        },
      });
      console.log('✅ Thumbnail uploaded:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error uploading thumbnail:', error);
      throw new Error(error.response?.data?.message || 'Failed to upload thumbnail');
    }
  }
}

export default new AdminModuleService();