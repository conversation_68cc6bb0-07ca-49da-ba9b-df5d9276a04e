import axios from 'axios';
import { auth } from '../firebase';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// Add request interceptor for authentication
api.interceptors.request.use(
  async (config) => {
    // Log API calls for debugging
    console.log(`🔄 API Call: ${config.method?.toUpperCase()} ${config.url}`);

    // Get Firebase auth token if user is authenticated
    const user = auth.currentUser;
    if (user) {
      try {
        const idToken = await user.getIdToken();
        config.headers.Authorization = `Bearer ${idToken}`;
      } catch (error) {
        console.error('Error getting auth token:', error);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Log successful responses
    console.log(`✅ API Success: ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  (error) => {
    // Log API errors
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response?.data || error.message);
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      // Redirect to login if unauthorized
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// API service for user management with simplified Firebase data
export const userService = {
  /**
   * Get all users with filtering and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise} Users data with pagination and stats
   */
  getAllUsers: async (params = {}) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  /**
   * Get user by Firebase UID
   * @param {string} uid - Firebase User UID
   * @returns {Promise} User data
   */
  getUserById: async (uid) => {
    const response = await api.get(`/users/${uid}`);
    return response.data;
  },

  /**
   * Create new user in Firebase Authentication
   * @param {Object} userData - User data including password
   * @returns {Promise} Created user
   */
  createUser: async (userData) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  /**
   * Update user data
   * @param {string} uid - Firebase User UID
   * @param {Object} userData - Updated user data
   * @returns {Promise} Updated user
   */
  updateUser: async (uid, userData) => {
    const response = await api.patch(`/users/${uid}`, userData);
    return response.data;
  },

  /**
   * Delete user
   * @param {string} uid - Firebase User UID
   * @returns {Promise} Success response
   */
  deleteUser: async (uid) => {
    const response = await api.delete(`/users/${uid}`);
    return response.data;
  },

  /**
   * Get current user's role and profile
   * @returns {Promise} User profile with role
   */
  getCurrentUserProfile: async () => {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const response = await api.get(`/users/${user.uid}/profile`);
    return response.data;
  },

  /**
   * Update user role (admin only)
   * @param {string} uid - User UID
   * @param {string} role - New role
   * @returns {Promise} Updated user
   */
  updateUserRole: async (uid, role) => {
    const response = await api.patch(`/users/${uid}/role`, { role });
    return response.data;
  },

  /**
   * Get user locked courses
   * @param {string} uid - User UID
   * @returns {Promise<Object>} User locked courses
   */
  getUserLockedCourses: async (uid) => {
    try {
      const response = await api.get(`/users/${uid}/locked-courses`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to get user coming-soon-courses');
    }
  },

  /**
   * Lock course for user
   * @param {string} uid - User UID
   * @param {string} courseId - Course ID to lock
   * @returns {Promise<Object>} Success response
   */
  lockCourseForUser: async (uid, courseId) => {
    try {
      const response = await api.post(`/users/${uid}/lock-course`, { courseId });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to lock course for user');
    }
  },

  /**
   * Unlock course for user
   * @param {string} uid - User UID
   * @param {string} courseId - Course ID to unlock
   * @returns {Promise<Object>} Success response
   */
  unlockCourseForUser: async (uid, courseId) => {
    try {
      const response = await api.delete(`/users/${uid}/unlock-course/${courseId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to unlock course for user');
    }
  }
};

export default userService;