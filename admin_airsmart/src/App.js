import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Experimental_CssVarsProvider as CssVarsProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box } from '@mui/material';

// Auth Components
import { AuthProvider } from './auth/auth.context';
import RequireAuth from './auth/RequireAuth';

// Components
import Sidebar from './components/Sidebar';
import TopBar from './components/TopBar';

// Pages
import UserManagement from './pages/UserManagement';
import FileManagement from './pages/FileManagement';
import CourseManagement from './pages/CourseManagement';
import ModuleManagement from './pages/ModuleManagement';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Unauthorized from './pages/Unauthorized';
import StepEditor from './pages/StepEditor';
import VideoStepEditor from './pages/VideoStepEditor';
import ImageStepEditor from './pages/ImageStepEditor';
// Theme
import AppTheme from './theme/AppTheme';
import EditVideoStep from './pages/EditVideoStep';
import EditImageStep from './pages/EditImageStep';
import EditStepEditor from './pages/EditStepEditor';

const DRAWER_WIDTH = 240;

// Protected Layout Component
function ProtectedLayout({ children }) {
  const [mobileOpen, setMobileOpen] = React.useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <TopBar 
        drawerWidth={DRAWER_WIDTH}
        onDrawerToggle={handleDrawerToggle}
      />
      <Sidebar 
        drawerWidth={DRAWER_WIDTH}
        mobileOpen={mobileOpen}
        onDrawerToggle={handleDrawerToggle}
      />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          mt: 8, // Account for top bar height
        }}
      >
        {children}
      </Box>
    </Box>
  );
}

function App() {
  return (
    <CssVarsProvider defaultMode="system">
      <AppTheme>
        <CssBaseline enableColorScheme />
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/unauthorized" element={<Unauthorized />} />
              
              {/* Protected Routes */}
              <Route path="/" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <Dashboard />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/dashboard" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <Dashboard />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/users" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <UserManagement />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/files" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <FileManagement />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/courses" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <CourseManagement />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/modules" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <ModuleManagement />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/step-editor" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <StepEditor />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/step-editor-video" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <VideoStepEditor />
                  </ProtectedLayout>
                </RequireAuth>
              } />
              <Route path="/step-editor-image" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <ImageStepEditor />
                  </ProtectedLayout>
                </RequireAuth>
              } />
               <Route path="/edit-step-editor" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <EditStepEditor />
                  </ProtectedLayout>
                </RequireAuth>
              } />
               <Route path="/edit-video-step" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <EditVideoStep />
                  </ProtectedLayout>
                </RequireAuth>
              } />
               <Route path="/edit-image-step" element={
                <RequireAuth>
                  <ProtectedLayout>
                    <EditImageStep />
                  </ProtectedLayout>
                </RequireAuth>
              } />
            </Routes>
          </Router>
        </AuthProvider>
      </AppTheme>
    </CssVarsProvider>
  );
}

export default App;
